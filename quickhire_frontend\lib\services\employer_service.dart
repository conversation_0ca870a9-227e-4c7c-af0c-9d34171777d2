// lib/services/employer_service.dart
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import '../models/employer_model.dart';

class EmployerService {
  static const String baseUrl = '${AppConfig.apiBaseUrl}/api/v1/employer';

  // Get headers with auth token
  Future<Map<String, String>> get _headers async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token') ?? '';
    
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Get multipart headers with auth token
  Future<Map<String, String>> get _multipartHeaders async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token') ?? '';
    
    return {
      'Authorization': 'Bearer $token',
    };
  }

  // Get employer profile
  Future<EmployerProfile> getProfile() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/profile'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return EmployerProfile.fromJson(data['data']);
      } else {
        throw Exception('Failed to load profile: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching profile: $e');
    }
  }

  // Update employer profile
  Future<EmployerProfile> updateProfile(EmployerProfile profile) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/profile'),
        headers: headers,
        body: json.encode(profile.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return EmployerProfile.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to update profile');
      }
    } catch (e) {
      throw Exception('Error updating profile: $e');
    }
  }

  // Upload profile picture
  Future<String> uploadProfilePicture(File imageFile) async {
    try {
      final headers = await _multipartHeaders;
      
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/profile/picture'),
      );
      
      request.headers.addAll(headers);
      request.files.add(
        await http.MultipartFile.fromPath(
          'profilePicture',
          imageFile.path,
        ),
      );

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['data']['profilePicture'];
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to upload image');
      }
    } catch (e) {
      throw Exception('Error uploading profile picture: $e');
    }
  }

  // Delete profile picture
  Future<bool> deleteProfilePicture() async {
    try {
      final headers = await _headers;
      final response = await http.delete(
        Uri.parse('$baseUrl/profile/picture'),
        headers: headers,
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error deleting profile picture: $e');
    }
  }

  // Get dashboard stats
  Future<DashboardStats> getDashboardStats() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/dashboard'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return DashboardStats.fromJson(data['data']);
      } else {
        throw Exception('Failed to load dashboard stats: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching dashboard stats: $e');
    }
  }

  // Get mock profile for testing
  EmployerProfile getMockProfile() {
    return EmployerProfile(
      id: '1',
      userId: 'user1',
      companyName: 'Tech Solutions Inc.',
      bio: 'We are a leading technology company specializing in innovative software solutions.',
      profilePicture: null,
      linkedinUrl: 'https://linkedin.com/company/tech-solutions',
      website: 'https://techsolutions.com',
      phoneNumber: '******-0123',
      contactInfo: ContactInfo(
        address: '123 Tech Street',
        city: 'San Francisco',
        country: 'USA',
      ),
      companySize: '51-200',
      industry: 'Technology',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    );
  }

  // Get mock dashboard stats for testing
  DashboardStats getMockDashboardStats() {
    return DashboardStats(
      totalProjects: 15,
      activeProjects: 8,
      completedProjects: 7,
      totalApplications: 45,
      pendingApplications: 12,
      acceptedApplications: 20,
    );
  }
}
