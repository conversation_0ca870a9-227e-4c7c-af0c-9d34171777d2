// lib/services/enhanced_messaging_service.dart
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import '../config/app_config.dart';
import '../models/message_model.dart';

class EnhancedMessagingService {
  static const String baseUrl = AppConfig.messagesEndpoint;
  static const String socketUrl = AppConfig.socketUrl;
  
  IO.Socket? _socket;
  Function(EnhancedMessage)? onMessageReceived;
  Function(String, MessageStatus)? onMessageStatusUpdate;
  Function(String)? onUserTyping;
  Function(String)? onUserStoppedTyping;

  // Get headers with auth token
  Future<Map<String, String>> get _headers async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token') ?? '';
    
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Initialize socket connection
  Future<void> initializeSocket() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token') ?? '';
    final userId = prefs.getString('user_id') ?? '';

    _socket = IO.io(socketUrl, <String, dynamic>{
      'transports': ['websocket'],
      'autoConnect': false,
      'extraHeaders': {'Authorization': 'Bearer $token'}
    });

    _socket!.connect();

    _socket!.on('connect', (_) {
      print('Connected to messaging socket');
      _socket!.emit('join', {'userId': userId});
    });

    _socket!.on('message', (data) {
      final message = EnhancedMessage.fromJson(data);
      onMessageReceived?.call(message);
    });

    _socket!.on('messageStatus', (data) {
      final messageId = data['messageId'];
      final status = MessageStatus.values.firstWhere(
        (e) => e.toString().split('.').last == data['status'],
        orElse: () => MessageStatus.sent,
      );
      onMessageStatusUpdate?.call(messageId, status);
    });

    _socket!.on('typing', (data) {
      onUserTyping?.call(data['userId']);
    });

    _socket!.on('stopTyping', (data) {
      onUserStoppedTyping?.call(data['userId']);
    });

    _socket!.on('disconnect', (_) {
      print('Disconnected from messaging socket');
    });
  }

  // Disconnect socket
  void disconnectSocket() {
    _socket?.disconnect();
    _socket = null;
  }

  // Join conversation room
  void joinConversation(String conversationId) {
    _socket?.emit('joinConversation', {'conversationId': conversationId});
  }

  // Leave conversation room
  void leaveConversation(String conversationId) {
    _socket?.emit('leaveConversation', {'conversationId': conversationId});
  }

  // Send typing indicator
  void sendTyping(String conversationId) {
    _socket?.emit('typing', {'conversationId': conversationId});
  }

  // Stop typing indicator
  void stopTyping(String conversationId) {
    _socket?.emit('stopTyping', {'conversationId': conversationId});
  }

  // Get conversations
  Future<List<ChatConversation>> getConversations() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/conversations'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final conversationsJson = data['data'] as List;
        return conversationsJson.map((json) => ChatConversation.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load conversations: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching conversations: $e');
    }
  }

  // Get messages for a conversation
  Future<List<EnhancedMessage>> getMessages(String conversationId, {int page = 1, int limit = 50}) async {
    try {
      final headers = await _headers;
      final queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      final uri = Uri.parse('$baseUrl/conversations/$conversationId/messages')
          .replace(queryParameters: queryParams);
      
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final messagesJson = data['data'] as List;
        return messagesJson.map((json) => EnhancedMessage.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load messages: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching messages: $e');
    }
  }

  // Send text message
  Future<EnhancedMessage> sendMessage(String conversationId, String content, {String? replyToId}) async {
    try {
      final headers = await _headers;
      final messageData = {
        'content': content,
        'type': 'text',
        if (replyToId != null) 'replyToId': replyToId,
      };

      final response = await http.post(
        Uri.parse('$baseUrl/conversations/$conversationId/messages'),
        headers: headers,
        body: json.encode(messageData),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return EnhancedMessage.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to send message');
      }
    } catch (e) {
      throw Exception('Error sending message: $e');
    }
  }

  // Upload file and send message
  Future<EnhancedMessage> sendFileMessage(
    String conversationId, 
    File file, 
    {String? caption, String? replyToId}
  ) async {
    try {
      final headers = await _headers;
      
      // First upload the file
      final uploadRequest = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/upload'),
      );
      
      uploadRequest.headers.addAll(headers);
      uploadRequest.files.add(await http.MultipartFile.fromPath('file', file.path));
      
      final uploadResponse = await uploadRequest.send();
      final uploadResponseBody = await uploadResponse.stream.bytesToString();
      
      if (uploadResponse.statusCode != 200) {
        throw Exception('Failed to upload file');
      }
      
      final uploadData = json.decode(uploadResponseBody);
      final attachment = MessageAttachment.fromJson(uploadData['data']);
      
      // Then send the message with attachment
      final messageData = {
        'content': caption ?? '',
        'type': attachment.isImage ? 'image' : 'file',
        'attachments': [attachment.toJson()],
        if (replyToId != null) 'replyToId': replyToId,
      };

      final response = await http.post(
        Uri.parse('$baseUrl/conversations/$conversationId/messages'),
        headers: headers,
        body: json.encode(messageData),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return EnhancedMessage.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to send file message');
      }
    } catch (e) {
      throw Exception('Error sending file message: $e');
    }
  }

  // Mark message as read
  Future<bool> markMessageAsRead(String conversationId, String messageId) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/conversations/$conversationId/messages/$messageId/read'),
        headers: headers,
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error marking message as read: $e');
    }
  }

  // Mark all messages in conversation as read
  Future<bool> markConversationAsRead(String conversationId) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/conversations/$conversationId/read'),
        headers: headers,
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error marking conversation as read: $e');
    }
  }

  // Delete message
  Future<bool> deleteMessage(String conversationId, String messageId) async {
    try {
      final headers = await _headers;
      final response = await http.delete(
        Uri.parse('$baseUrl/conversations/$conversationId/messages/$messageId'),
        headers: headers,
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error deleting message: $e');
    }
  }

  // Edit message
  Future<EnhancedMessage> editMessage(String conversationId, String messageId, String newContent) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/conversations/$conversationId/messages/$messageId'),
        headers: headers,
        body: json.encode({'content': newContent}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return EnhancedMessage.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to edit message');
      }
    } catch (e) {
      throw Exception('Error editing message: $e');
    }
  }

  // Create or get conversation
  Future<ChatConversation> createOrGetConversation(String projectId, String otherUserId) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse('$baseUrl/conversations'),
        headers: headers,
        body: json.encode({
          'projectId': projectId,
          'participantId': otherUserId,
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = json.decode(response.body);
        return ChatConversation.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to create conversation');
      }
    } catch (e) {
      throw Exception('Error creating conversation: $e');
    }
  }

  // Get mock conversations for development
  List<ChatConversation> getMockConversations() {
    return [
      ChatConversation(
        id: '1',
        projectId: 'proj1',
        projectTitle: 'Mobile App Development',
        employerId: 'emp1',
        employerName: 'John Employer',
        jobSeekerId: 'js1',
        jobSeekerName: 'Jane Developer',
        lastMessage: EnhancedMessage(
          id: 'msg1',
          senderId: 'js1',
          senderName: 'Jane Developer',
          receiverId: 'emp1',
          content: 'Thank you for accepting my application! When can we schedule a meeting?',
          type: MessageType.text,
          status: MessageStatus.read,
          timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
          readAt: DateTime.now().subtract(const Duration(minutes: 25)),
        ),
        unreadCount: 0,
        lastActivity: DateTime.now().subtract(const Duration(minutes: 30)),
        isActive: true,
      ),
      ChatConversation(
        id: '2',
        projectId: 'proj2',
        projectTitle: 'Website Redesign',
        employerId: 'emp2',
        employerName: 'Sarah Company',
        jobSeekerId: 'js2',
        jobSeekerName: 'Mike Designer',
        lastMessage: EnhancedMessage(
          id: 'msg2',
          senderId: 'emp2',
          senderName: 'Sarah Company',
          receiverId: 'js2',
          content: 'I\'ve reviewed your portfolio. Can you show me some examples of e-commerce sites?',
          type: MessageType.text,
          status: MessageStatus.delivered,
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        unreadCount: 1,
        lastActivity: DateTime.now().subtract(const Duration(hours: 2)),
        isActive: true,
      ),
    ];
  }

  // Get mock messages for development
  List<EnhancedMessage> getMockMessages() {
    return [
      EnhancedMessage(
        id: '1',
        senderId: 'emp1',
        senderName: 'John Employer',
        receiverId: 'js1',
        content: 'Hi Jane! I\'ve reviewed your application and I\'m impressed with your Flutter experience.',
        type: MessageType.text,
        status: MessageStatus.read,
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        readAt: DateTime.now().subtract(const Duration(hours: 1, minutes: 55)),
      ),
      EnhancedMessage(
        id: '2',
        senderId: 'js1',
        senderName: 'Jane Developer',
        receiverId: 'emp1',
        content: 'Thank you! I\'m very excited about this project. I have some questions about the requirements.',
        type: MessageType.text,
        status: MessageStatus.read,
        timestamp: DateTime.now().subtract(const Duration(hours: 1, minutes: 50)),
        readAt: DateTime.now().subtract(const Duration(hours: 1, minutes: 45)),
      ),
      EnhancedMessage(
        id: '3',
        senderId: 'emp1',
        senderName: 'John Employer',
        receiverId: 'js1',
        content: 'Of course! Feel free to ask anything. Also, would you like to schedule a video call to discuss the project in detail?',
        type: MessageType.text,
        status: MessageStatus.read,
        timestamp: DateTime.now().subtract(const Duration(hours: 1, minutes: 40)),
        readAt: DateTime.now().subtract(const Duration(hours: 1, minutes: 35)),
      ),
      EnhancedMessage(
        id: '4',
        senderId: 'js1',
        senderName: 'Jane Developer',
        receiverId: 'emp1',
        content: 'That would be great! I\'m available tomorrow afternoon or Thursday morning.',
        type: MessageType.text,
        status: MessageStatus.read,
        timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        readAt: DateTime.now().subtract(const Duration(minutes: 25)),
      ),
    ];
  }
}
