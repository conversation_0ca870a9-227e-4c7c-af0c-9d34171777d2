// routes/jobSeekerRoutes.js
const express = require('express');
const {
  getProfile,
  updateProfile,
  uploadProfilePicture,
  deleteProfilePicture,
  getDashboardStats
} = require('../controllers/jobSeekerController');
const { protect, authorize } = require('../middlewares/auth');
const { upload } = require('../utils/fileUpload');

const router = express.Router();

// All routes require authentication and job seeker role
router.use(protect);
router.use(authorize('jobseeker'));

// Profile routes
router.get('/profile', getProfile);
router.put('/profile', updateProfile);

// Profile picture routes
router.post('/profile/picture', upload.single('profilePicture'), uploadProfilePicture);
router.delete('/profile/picture', deleteProfilePicture);

// Dashboard routes
router.get('/dashboard', getDashboardStats);

module.exports = router;
