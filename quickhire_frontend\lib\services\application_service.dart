// lib/services/application_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import '../models/application_model.dart';

class ApplicationService {
  static const String baseUrl = AppConfig.projectsEndpoint;

  // Get headers with auth token
  Future<Map<String, String>> get _headers async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token') ?? '';
    
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Submit application to a project
  Future<bool> submitApplication(ApplicationSubmission application) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse('$baseUrl/${application.projectId}/apply'),
        headers: headers,
        body: json.encode(application.toJson()),
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to submit application');
      }
    } catch (e) {
      throw Exception('Error submitting application: $e');
    }
  }

  // Get applications for a specific project (for employers)
  Future<List<DetailedApplication>> getProjectApplications(String projectId) async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/$projectId/applications'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final applicationsJson = data['data'] as List;
        return applicationsJson.map((json) => DetailedApplication.fromJson(json)).toList();
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to load applications');
      }
    } catch (e) {
      throw Exception('Error fetching applications: $e');
    }
  }

  // Update application status (accept/reject)
  Future<bool> updateApplicationStatus(String projectId, String applicationId, String status) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/$projectId/applications/$applicationId'),
        headers: headers,
        body: json.encode({'status': status}),
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to update application status');
      }
    } catch (e) {
      throw Exception('Error updating application status: $e');
    }
  }

  // Get job seeker's applications
  Future<List<DetailedApplication>> getMyApplications() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/jobseeker/applications'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final applicationsJson = data['data'] as List;
        return applicationsJson.map((json) => DetailedApplication.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load applications: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching my applications: $e');
    }
  }

  // Withdraw application
  Future<bool> withdrawApplication(String projectId, String applicationId) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/$projectId/applications/$applicationId'),
        headers: headers,
        body: json.encode({'status': 'withdrawn'}),
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error withdrawing application: $e');
    }
  }

  // Get application statistics for a project
  Future<ApplicationStats> getApplicationStats(String projectId) async {
    try {
      final applications = await getProjectApplications(projectId);
      
      final totalApplications = applications.length;
      final pendingApplications = applications.where((app) => app.isPending).length;
      final acceptedApplications = applications.where((app) => app.isAccepted).length;
      final rejectedApplications = applications.where((app) => app.isRejected).length;
      
      final budgets = applications
          .where((app) => app.proposedBudget != null)
          .map((app) => app.proposedBudget!)
          .toList();
      
      final averageProposedBudget = budgets.isNotEmpty 
          ? budgets.reduce((a, b) => a + b) / budgets.length 
          : 0.0;

      // Count experience levels
      final experienceLevels = <String, int>{};
      for (final app in applications) {
        final level = app.jobSeeker.experienceLevel;
        experienceLevels[level] = (experienceLevels[level] ?? 0) + 1;
      }
      
      final mostCommonExperienceLevel = experienceLevels.isNotEmpty
          ? experienceLevels.entries.reduce((a, b) => a.value > b.value ? a : b).key
          : 'entry';

      return ApplicationStats(
        totalApplications: totalApplications,
        pendingApplications: pendingApplications,
        acceptedApplications: acceptedApplications,
        rejectedApplications: rejectedApplications,
        averageProposedBudget: averageProposedBudget,
        mostCommonExperienceLevel: mostCommonExperienceLevel,
      );
    } catch (e) {
      throw Exception('Error calculating application stats: $e');
    }
  }

  // Get mock applications for testing
  List<DetailedApplication> getMockApplications() {
    return [
      DetailedApplication(
        id: '1',
        jobSeeker: JobSeekerInfo(
          id: 'js1',
          name: 'John Smith',
          email: '<EMAIL>',
          location: 'New York, NY',
          bio: 'Experienced Flutter developer with 3+ years of experience',
          skills: ['Flutter', 'Dart', 'Firebase', 'REST APIs'],
          experienceLevel: 'intermediate',
          rating: 4.8,
          completedProjects: 12,
        ),
        status: 'pending',
        appliedAt: DateTime.now().subtract(const Duration(hours: 2)),
        coverLetter: 'I am very interested in this project and believe my experience with Flutter development makes me a perfect fit. I have successfully completed similar projects in the past.',
        proposedBudget: 2500.0,
        estimatedDuration: '6-8 weeks',
        portfolio: [
          PortfolioItem(
            title: 'E-commerce Mobile App',
            description: 'Flutter app with payment integration',
            url: 'https://github.com/johnsmith/ecommerce-app',
          ),
        ],
      ),
      DetailedApplication(
        id: '2',
        jobSeeker: JobSeekerInfo(
          id: 'js2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          location: 'San Francisco, CA',
          bio: 'Senior mobile developer specializing in cross-platform solutions',
          skills: ['Flutter', 'React Native', 'iOS', 'Android'],
          experienceLevel: 'expert',
          rating: 4.9,
          completedProjects: 25,
        ),
        status: 'accepted',
        appliedAt: DateTime.now().subtract(const Duration(days: 1)),
        coverLetter: 'Hello! I have extensive experience in mobile app development and would love to work on your project. My portfolio includes several successful Flutter applications.',
        proposedBudget: 3200.0,
        estimatedDuration: '4-6 weeks',
        portfolio: [
          PortfolioItem(
            title: 'Healthcare Management App',
            description: 'Complete healthcare solution with patient management',
            url: 'https://github.com/sarahjohnson/healthcare-app',
          ),
          PortfolioItem(
            title: 'Social Media Platform',
            description: 'Instagram-like social media app',
            url: 'https://github.com/sarahjohnson/social-app',
          ),
        ],
        reviewedAt: DateTime.now().subtract(const Duration(hours: 12)),
      ),
      DetailedApplication(
        id: '3',
        jobSeeker: JobSeekerInfo(
          id: 'js3',
          name: 'Mike Chen',
          email: '<EMAIL>',
          location: 'Austin, TX',
          bio: 'Full-stack developer with mobile expertise',
          skills: ['Flutter', 'Node.js', 'MongoDB', 'AWS'],
          experienceLevel: 'intermediate',
          rating: 4.6,
          completedProjects: 8,
        ),
        status: 'rejected',
        appliedAt: DateTime.now().subtract(const Duration(days: 2)),
        coverLetter: 'I am interested in this opportunity and have relevant experience in mobile development.',
        proposedBudget: 1800.0,
        estimatedDuration: '8-10 weeks',
        portfolio: [],
        reviewedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];
  }
}
