// lib/screens/jobseeker_applications_screen.dart
import 'package:flutter/material.dart';
import '../models/jobseeker_model.dart';
import '../services/jobseeker_service.dart';
import 'project_review_screen.dart';

class JobSeekerApplicationsScreen extends StatefulWidget {
  static const String routeName = '/jobseeker-applications';
  static const String id = 'JobSeekerApplicationsScreen';

  const JobSeekerApplicationsScreen({Key? key}) : super(key: key);

  @override
  State<JobSeekerApplicationsScreen> createState() => _JobSeekerApplicationsScreenState();
}

class _JobSeekerApplicationsScreenState extends State<JobSeekerApplicationsScreen>
    with SingleTickerProviderStateMixin {
  final JobSeekerService _jobSeekerService = JobSeekerService();
  late TabController _tabController;

  List<JobSeekerApplication> _allApplications = [];
  List<JobSeekerApplication> _pendingApplications = [];
  List<JobSeekerApplication> _acceptedApplications = [];
  List<JobSeekerApplication> _rejectedApplications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadApplications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadApplications() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // For now, use mock data. In production, this would call the API
      final applications = _jobSeekerService.getMockApplications();
      
      setState(() {
        _allApplications = applications;
        _pendingApplications = applications.where((app) => app.isPending).toList();
        _acceptedApplications = applications.where((app) => app.isAccepted).toList();
        _rejectedApplications = applications.where((app) => app.isRejected).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showError('Failed to load applications: $e');
    }
  }

  Future<void> _withdrawApplication(JobSeekerApplication application) async {
    final confirmed = await _showConfirmDialog(
      'Withdraw Application',
      'Are you sure you want to withdraw your application for "${application.projectTitle}"?',
    );

    if (confirmed) {
      try {
        final success = await _jobSeekerService.withdrawApplication(application.id);
        if (success) {
          _showSuccess('Application withdrawn successfully');
          _loadApplications(); // Reload applications
        }
      } catch (e) {
        _showError('Failed to withdraw application: $e');
      }
    }
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Applications'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              text: 'All (${_allApplications.length})',
            ),
            Tab(
              text: 'Pending (${_pendingApplications.length})',
            ),
            Tab(
              text: 'Accepted (${_acceptedApplications.length})',
            ),
            Tab(
              text: 'Rejected (${_rejectedApplications.length})',
            ),
          ],
        ),
      ),
      body: _isLoading ? _buildLoadingState() : _buildTabView(),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(color: Colors.blue),
    );
  }

  Widget _buildTabView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildApplicationsList(_allApplications),
        _buildApplicationsList(_pendingApplications),
        _buildApplicationsList(_acceptedApplications),
        _buildApplicationsList(_rejectedApplications),
      ],
    );
  }

  Widget _buildApplicationsList(List<JobSeekerApplication> applications) {
    if (applications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No applications found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start applying to projects to see them here',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadApplications,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: applications.length,
        itemBuilder: (context, index) {
          final application = applications[index];
          return _buildApplicationCard(application);
        },
      ),
    );
  }

  Widget _buildApplicationCard(JobSeekerApplication application) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        application.projectTitle,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'by ${application.employerName}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(application.status),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    application.statusDisplayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Application details
            if (application.proposedBudget != null)
              _buildDetailRow(
                'Proposed Budget',
                '\$${application.proposedBudget!.toStringAsFixed(0)}',
                Icons.attach_money,
              ),
            if (application.estimatedDuration != null)
              _buildDetailRow(
                'Estimated Duration',
                application.estimatedDuration!,
                Icons.schedule,
              ),
            _buildDetailRow(
              'Applied',
              _getTimeAgo(application.appliedAt),
              Icons.calendar_today,
            ),
            if (application.reviewedAt != null)
              _buildDetailRow(
                'Reviewed',
                _getTimeAgo(application.reviewedAt!),
                Icons.visibility,
              ),

            // Cover letter preview
            if (application.coverLetter != null) ...[
              const SizedBox(height: 16),
              const Text(
                'Cover Letter',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  application.coverLetter!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],

            // Actions
            const SizedBox(height: 16),
            _buildActionButtons(application),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(JobSeekerApplication application) {
    return Row(
      children: [
        // View project details
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              _showApplicationDetails(application);
            },
            icon: const Icon(Icons.info_outline),
            label: const Text('Details'),
          ),
        ),
        const SizedBox(width: 12),

        // Status-specific actions
        if (application.isPending)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _withdrawApplication(application),
              icon: const Icon(Icons.cancel),
              label: const Text('Withdraw'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          )
        else if (application.isAccepted)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ProjectReviewScreen(
                      projectId: application.projectId,
                      projectTitle: application.projectTitle,
                      employerName: application.employerName,
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.star),
              label: const Text('Review'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          )
        else
          Expanded(
            child: Container(), // Empty space for rejected/withdrawn
          ),
      ],
    );
  }

  void _showApplicationDetails(JobSeekerApplication application) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  application.projectTitle,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'by ${application.employerName}',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(application.status),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    application.statusDisplayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                if (application.coverLetter != null) ...[
                  const Text(
                    'Cover Letter',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(application.coverLetter!),
                  const SizedBox(height: 16),
                ],
                if (application.proposedBudget != null) ...[
                  Text(
                    'Proposed Budget: \$${application.proposedBudget!.toStringAsFixed(0)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
                if (application.estimatedDuration != null) ...[
                  Text(
                    'Estimated Duration: ${application.estimatedDuration}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
                Text(
                  'Applied: ${_getFullDate(application.appliedAt)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (application.reviewedAt != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Reviewed: ${_getFullDate(application.reviewedAt!)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'accepted':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      case 'withdrawn':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String _getFullDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
