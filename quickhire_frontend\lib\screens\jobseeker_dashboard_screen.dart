// lib/screens/jobseeker_dashboard_screen.dart
import 'package:flutter/material.dart';
import '../models/jobseeker_model.dart';
import '../services/jobseeker_service.dart';
import 'jobseeker_profile_screen.dart';
import 'project_discovery_screen.dart';
import 'jobseeker_applications_screen.dart';

class JobSeekerDashboardScreen extends StatefulWidget {
  static const String routeName = '/jobseeker-dashboard';
  static const String id = 'JobSeekerDashboardScreen';

  const JobSeekerDashboardScreen({Key? key}) : super(key: key);

  @override
  State<JobSeekerDashboardScreen> createState() => _JobSeekerDashboardScreenState();
}

class _JobSeekerDashboardScreenState extends State<JobSeekerDashboardScreen> {
  final JobSeekerService _jobSeekerService = JobSeekerService();

  JobSeekerModel? _jobSeeker;
  List<JobSeekerApplication> _recentApplications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // For now, use mock data. In production, this would call the API
      final applications = _jobSeekerService.getMockApplications();
      
      setState(() {
        _recentApplications = applications.take(3).toList();
        _isLoading = false;
      });

      // Load profile separately
      _loadProfile();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showError('Failed to load dashboard: $e');
    }
  }

  Future<void> _loadProfile() async {
    try {
      final jobSeeker = await _jobSeekerService.getProfile();
      setState(() {
        _jobSeeker = jobSeeker;
      });
    } catch (e) {
      // Profile loading failed, but don't show error as it's secondary
    }
  }

  Future<void> _toggleAvailability() async {
    if (_jobSeeker == null) return;

    try {
      final newStatus = !_jobSeeker!.isAvailable;
      final success = await _jobSeekerService.updateAvailability(newStatus);
      
      if (success) {
        setState(() {
          _jobSeeker = _jobSeeker!.copyWith(isAvailable: newStatus);
        });
        _showSuccess('Availability updated successfully');
      }
    } catch (e) {
      _showError('Failed to update availability: $e');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.pushNamed(context, JobSeekerProfileScreen.routeName);
            },
            icon: const Icon(Icons.person),
          ),
        ],
      ),
      body: _isLoading ? _buildLoadingState() : _buildDashboard(),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, ProjectDiscoveryScreen.routeName);
        },
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.search),
        label: const Text('Find Projects'),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(color: Colors.blue),
    );
  }

  Widget _buildDashboard() {
    return RefreshIndicator(
      onRefresh: _loadDashboardData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section
            _buildWelcomeSection(),
            const SizedBox(height: 24),

            // Quick stats
            _buildQuickStats(),
            const SizedBox(height: 24),

            // Profile completion
            if (_jobSeeker != null) _buildProfileCompletion(),
            if (_jobSeeker != null) const SizedBox(height: 24),

            // Availability toggle
            if (_jobSeeker != null) _buildAvailabilitySection(),
            if (_jobSeeker != null) const SizedBox(height: 24),

            // Recent applications
            _buildRecentApplications(),
            const SizedBox(height: 24),

            // Quick actions
            _buildQuickActions(),
            const SizedBox(height: 100), // Space for FAB
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Colors.blue,
              backgroundImage: _jobSeeker?.hasProfilePicture == true
                  ? NetworkImage(_jobSeeker!.profilePicture!)
                  : null,
              child: _jobSeeker?.hasProfilePicture != true
                  ? Text(
                      _jobSeeker?.name.substring(0, 1).toUpperCase() ?? 'U',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back,',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    _jobSeeker?.name ?? 'Job Seeker',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_jobSeeker != null)
                    Text(
                      _jobSeeker!.availabilityStatus,
                      style: TextStyle(
                        fontSize: 14,
                        color: _jobSeeker!.isAvailable ? Colors.green : Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Applications',
            '${_recentApplications.length}',
            Icons.send,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Accepted',
            '${_recentApplications.where((app) => app.isAccepted).length}',
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Rating',
            _jobSeeker?.ratingDisplay ?? 'N/A',
            Icons.star,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileCompletion() {
    final completionPercentage = _jobSeeker!.profileCompletionPercentage;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  completionPercentage == 100 ? Icons.check_circle : Icons.info,
                  color: completionPercentage == 100 ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Profile Completion',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  '${completionPercentage.toInt()}%',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: completionPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                completionPercentage == 100 ? Colors.green : Colors.orange,
              ),
            ),
            if (completionPercentage < 100) ...[
              const SizedBox(height: 8),
              Text(
                'Complete your profile to get more opportunities',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAvailabilitySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              _jobSeeker!.isAvailable ? Icons.work : Icons.work_off,
              color: _jobSeeker!.isAvailable ? Colors.green : Colors.orange,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Availability Status',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _jobSeeker!.availabilityStatus,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: _jobSeeker!.isAvailable,
              onChanged: (_) => _toggleAvailability(),
              activeColor: Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentApplications() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Recent Applications',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: () {
                Navigator.pushNamed(context, JobSeekerApplicationsScreen.routeName);
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (_recentApplications.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Center(
                child: Column(
                  children: [
                    Icon(Icons.inbox, size: 48, color: Colors.grey[400]),
                    const SizedBox(height: 8),
                    Text(
                      'No applications yet',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamed(context, ProjectDiscoveryScreen.routeName);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Find Projects'),
                    ),
                  ],
                ),
              ),
            ),
          )
        else
          ...List.generate(_recentApplications.length, (index) {
            final application = _recentApplications[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: _getStatusColor(application.status),
                  child: Icon(
                    _getStatusIcon(application.status),
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                title: Text(
                  application.projectTitle,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                subtitle: Text(
                  'by ${application.employerName} • ${application.statusDisplayName}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                trailing: Text(
                  _getTimeAgo(application.appliedAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
                onTap: () {
                  // Navigate to application details
                },
              ),
            );
          }),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'Edit Profile',
                Icons.person_outline,
                Colors.blue,
                () => Navigator.pushNamed(context, JobSeekerProfileScreen.routeName),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                'My Applications',
                Icons.list_alt,
                Colors.green,
                () => Navigator.pushNamed(context, JobSeekerApplicationsScreen.routeName),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'accepted':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      case 'withdrawn':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.hourglass_empty;
      case 'accepted':
        return Icons.check;
      case 'rejected':
        return Icons.close;
      case 'withdrawn':
        return Icons.undo;
      default:
        return Icons.send;
    }
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
