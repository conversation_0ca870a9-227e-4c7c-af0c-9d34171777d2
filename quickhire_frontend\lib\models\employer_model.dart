// lib/models/employer_model.dart

class EmployerProfile {
  final String id;
  final String userId;
  final String companyName;
  final String? bio;
  final String? profilePicture;
  final String? linkedinUrl;
  final String? website;
  final String? phoneNumber;
  final ContactInfo? contactInfo;
  final String? companySize;
  final String? industry;
  final DateTime createdAt;
  final DateTime updatedAt;

  EmployerProfile({
    required this.id,
    required this.userId,
    required this.companyName,
    this.bio,
    this.profilePicture,
    this.linkedinUrl,
    this.website,
    this.phoneNumber,
    this.contactInfo,
    this.companySize,
    this.industry,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployerProfile.fromJson(Map<String, dynamic> json) {
    return EmployerProfile(
      id: json['_id'] ?? json['id'] ?? '',
      userId: json['user']?['_id'] ?? json['user'] ?? '',
      companyName: json['companyName'] ?? '',
      bio: json['bio'],
      profilePicture: json['profilePicture'],
      linkedinUrl: json['linkedinUrl'],
      website: json['website'],
      phoneNumber: json['phoneNumber'],
      contactInfo: json['contactInfo'] != null 
          ? ContactInfo.fromJson(json['contactInfo']) 
          : null,
      companySize: json['companySize'],
      industry: json['industry'],
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'companyName': companyName,
      'bio': bio,
      'linkedinUrl': linkedinUrl,
      'website': website,
      'phoneNumber': phoneNumber,
      'contactInfo': contactInfo?.toJson(),
      'companySize': companySize,
      'industry': industry,
    };
  }

  EmployerProfile copyWith({
    String? id,
    String? userId,
    String? companyName,
    String? bio,
    String? profilePicture,
    String? linkedinUrl,
    String? website,
    String? phoneNumber,
    ContactInfo? contactInfo,
    String? companySize,
    String? industry,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployerProfile(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      companyName: companyName ?? this.companyName,
      bio: bio ?? this.bio,
      profilePicture: profilePicture ?? this.profilePicture,
      linkedinUrl: linkedinUrl ?? this.linkedinUrl,
      website: website ?? this.website,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      contactInfo: contactInfo ?? this.contactInfo,
      companySize: companySize ?? this.companySize,
      industry: industry ?? this.industry,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class ContactInfo {
  final String? address;
  final String? city;
  final String? country;

  ContactInfo({
    this.address,
    this.city,
    this.country,
  });

  factory ContactInfo.fromJson(Map<String, dynamic> json) {
    return ContactInfo(
      address: json['address'],
      city: json['city'],
      country: json['country'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'city': city,
      'country': country,
    };
  }
}

class User {
  final String id;
  final String name;
  final String email;
  final String role;
  final String location;
  final bool isVerified;
  final DateTime createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    required this.location,
    required this.isVerified,
    required this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      role: json['role'] ?? '',
      location: json['location'] ?? '',
      isVerified: json['isVerified'] ?? false,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
    );
  }
}

class DashboardStats {
  final int totalProjects;
  final int activeProjects;
  final int completedProjects;
  final int totalApplications;
  final int pendingApplications;
  final int acceptedApplications;

  DashboardStats({
    required this.totalProjects,
    required this.activeProjects,
    required this.completedProjects,
    required this.totalApplications,
    required this.pendingApplications,
    required this.acceptedApplications,
  });

  factory DashboardStats.fromJson(Map<String, dynamic> json) {
    return DashboardStats(
      totalProjects: json['totalProjects'] ?? 0,
      activeProjects: json['activeProjects'] ?? 0,
      completedProjects: json['completedProjects'] ?? 0,
      totalApplications: json['totalApplications'] ?? 0,
      pendingApplications: json['pendingApplications'] ?? 0,
      acceptedApplications: json['acceptedApplications'] ?? 0,
    );
  }
}
