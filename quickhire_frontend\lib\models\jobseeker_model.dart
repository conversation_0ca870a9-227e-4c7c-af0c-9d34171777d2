// lib/models/jobseeker_model.dart

class JobSeekerModel {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String location;
  final String? bio;
  final List<String> skills;
  final String experienceLevel;
  final String? cvUrl;
  final String? profilePicture;
  final double rating;
  final int completedProjects;
  final bool isAvailable;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String>? portfolio;
  final Map<String, dynamic>? socialLinks;

  JobSeekerModel({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    required this.location,
    this.bio,
    required this.skills,
    required this.experienceLevel,
    this.cvUrl,
    this.profilePicture,
    required this.rating,
    required this.completedProjects,
    required this.isAvailable,
    required this.createdAt,
    required this.updatedAt,
    this.portfolio,
    this.socialLinks,
  });

  factory JobSeekerModel.fromJson(Map<String, dynamic> json) {
    return JobSeekerModel(
      id: json['id'] ?? json['_id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      location: json['location'] ?? '',
      bio: json['bio'],
      skills: json['skills'] != null ? List<String>.from(json['skills']) : [],
      experienceLevel: json['experienceLevel'] ?? 'entry',
      cvUrl: json['cvUrl'],
      profilePicture: json['profilePicture'],
      rating: (json['rating'] ?? 0.0).toDouble(),
      completedProjects: json['completedProjects'] ?? 0,
      isAvailable: json['isAvailable'] ?? true,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
      portfolio: json['portfolio'] != null
          ? List<String>.from(json['portfolio'])
          : null,
      socialLinks: json['socialLinks'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'location': location,
      'bio': bio,
      'skills': skills,
      'experienceLevel': experienceLevel,
      'cvUrl': cvUrl,
      'profilePicture': profilePicture,
      'rating': rating,
      'completedProjects': completedProjects,
      'isAvailable': isAvailable,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'portfolio': portfolio,
      'socialLinks': socialLinks,
    };
  }

  JobSeekerModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? location,
    String? bio,
    List<String>? skills,
    String? experienceLevel,
    String? cvUrl,
    String? profilePicture,
    double? rating,
    int? completedProjects,
    bool? isAvailable,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? portfolio,
    Map<String, dynamic>? socialLinks,
  }) {
    return JobSeekerModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      location: location ?? this.location,
      bio: bio ?? this.bio,
      skills: skills ?? this.skills,
      experienceLevel: experienceLevel ?? this.experienceLevel,
      cvUrl: cvUrl ?? this.cvUrl,
      profilePicture: profilePicture ?? this.profilePicture,
      rating: rating ?? this.rating,
      completedProjects: completedProjects ?? this.completedProjects,
      isAvailable: isAvailable ?? this.isAvailable,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      portfolio: portfolio ?? this.portfolio,
      socialLinks: socialLinks ?? this.socialLinks,
    );
  }

  // Helper getters
  String get experienceLevelDisplay {
    switch (experienceLevel) {
      case 'entry':
        return 'Entry Level (0-2 years)';
      case 'intermediate':
        return 'Intermediate (2-5 years)';
      case 'expert':
        return 'Expert (5+ years)';
      default:
        return experienceLevel.toUpperCase();
    }
  }

  String get ratingDisplay {
    if (rating == 0.0) return 'No ratings yet';
    return '${rating.toStringAsFixed(1)} ⭐';
  }

  String get availabilityStatus {
    return isAvailable ? 'Available for work' : 'Not available';
  }

  bool get hasCV => cvUrl != null && cvUrl!.isNotEmpty;
  bool get hasProfilePicture => profilePicture != null && profilePicture!.isNotEmpty;
  bool get hasBio => bio != null && bio!.isNotEmpty;
  bool get hasPortfolio => portfolio != null && portfolio!.isNotEmpty;
  bool get hasPhone => phone != null && phone!.isNotEmpty;

  // Validation methods
  bool get isProfileComplete {
    return name.isNotEmpty &&
           email.isNotEmpty &&
           location.isNotEmpty &&
           skills.isNotEmpty &&
           hasCV;
  }

  double get profileCompletionPercentage {
    int completedFields = 0;
    int totalFields = 8;

    if (name.isNotEmpty) completedFields++;
    if (email.isNotEmpty) completedFields++;
    if (location.isNotEmpty) completedFields++;
    if (skills.isNotEmpty) completedFields++;
    if (hasCV) completedFields++;
    if (hasBio) completedFields++;
    if (hasProfilePicture) completedFields++;
    if (hasPhone) completedFields++;

    return (completedFields / totalFields) * 100;
  }

  List<String> get missingProfileFields {
    List<String> missing = [];

    if (name.isEmpty) missing.add('Name');
    if (email.isEmpty) missing.add('Email');
    if (location.isEmpty) missing.add('Location');
    if (skills.isEmpty) missing.add('Skills');
    if (!hasCV) missing.add('CV/Resume');
    if (!hasBio) missing.add('Professional Bio');
    if (!hasProfilePicture) missing.add('Profile Picture');
    if (!hasPhone) missing.add('Phone Number');

    return missing;
  }

  @override
  String toString() {
    return 'JobSeekerModel(id: $id, name: $name, email: $email, experienceLevel: $experienceLevel, rating: $rating)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JobSeekerModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Legacy JobSeeker class for backward compatibility
class JobSeeker {
  final String id;
  final String location;
  final String experience;
  final String education;
  final String salary;
  final String imageUrl;

  JobSeeker({
    required this.id,
    required this.location,
    required this.experience,
    required this.education,
    required this.salary,
    required this.imageUrl,
  });
}

// Job Seeker Application Model
class JobSeekerApplication {
  final String id;
  final String projectId;
  final String projectTitle;
  final String employerId;
  final String employerName;
  final String status;
  final DateTime appliedAt;
  final String? coverLetter;
  final double? proposedBudget;
  final String? estimatedDuration;
  final List<Map<String, String>>? portfolio;
  final DateTime? reviewedAt;

  JobSeekerApplication({
    required this.id,
    required this.projectId,
    required this.projectTitle,
    required this.employerId,
    required this.employerName,
    required this.status,
    required this.appliedAt,
    this.coverLetter,
    this.proposedBudget,
    this.estimatedDuration,
    this.portfolio,
    this.reviewedAt,
  });

  factory JobSeekerApplication.fromJson(Map<String, dynamic> json) {
    return JobSeekerApplication(
      id: json['id'] ?? json['_id'] ?? '',
      projectId: json['projectId'] ?? '',
      projectTitle: json['projectTitle'] ?? '',
      employerId: json['employerId'] ?? '',
      employerName: json['employerName'] ?? '',
      status: json['status'] ?? 'pending',
      appliedAt: json['appliedAt'] != null
          ? DateTime.parse(json['appliedAt'])
          : DateTime.now(),
      coverLetter: json['coverLetter'],
      proposedBudget: json['proposedBudget']?.toDouble(),
      estimatedDuration: json['estimatedDuration'],
      portfolio: json['portfolio'] != null
          ? List<Map<String, String>>.from(json['portfolio'])
          : null,
      reviewedAt: json['reviewedAt'] != null
          ? DateTime.parse(json['reviewedAt'])
          : null,
    );
  }

  bool get isPending => status == 'pending';
  bool get isAccepted => status == 'accepted';
  bool get isRejected => status == 'rejected';
  bool get isWithdrawn => status == 'withdrawn';

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'Under Review';
      case 'accepted':
        return 'Accepted';
      case 'rejected':
        return 'Not Selected';
      case 'withdrawn':
        return 'Withdrawn';
      default:
        return status.toUpperCase();
    }
  }
}