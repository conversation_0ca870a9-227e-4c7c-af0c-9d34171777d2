// lib/services/file_upload_service.dart
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../config/app_config.dart';

class FileUploadService {
  static const String baseUrl = '${AppConfig.apiBaseUrl}/api/v1/upload';

  // Get headers with auth token
  Future<Map<String, String>> get _headers async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token') ?? '';
    
    return {
      'Authorization': 'Bearer $token',
    };
  }

  // Upload file (CV, profile picture, etc.) - File version
  Future<String> uploadFile(File file, String fileType) async {
    try {
      final headers = await _headers;

      final request = http.MultipartRequest('POST', Uri.parse(baseUrl));
      request.headers.addAll(headers);

      // Add file to request
      request.files.add(await http.MultipartFile.fromPath(
        'file',
        file.path,
        filename: _generateFileName(file, fileType),
      ));

      // Add file type
      request.fields['fileType'] = fileType;

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final data = json.decode(responseBody);
        return data['fileUrl'] ?? data['url'] ?? '';
      } else {
        final errorData = json.decode(responseBody);
        throw Exception(errorData['message'] ?? 'Failed to upload file');
      }
    } catch (e) {
      throw Exception('Error uploading file: $e');
    }
  }

  // Upload PlatformFile (for web compatibility)
  Future<String> uploadPlatformFile(PlatformFile file, String fileType) async {
    try {
      final headers = await _headers;

      final request = http.MultipartRequest('POST', Uri.parse(baseUrl));
      request.headers.addAll(headers);

      // Add file to request
      if (kIsWeb) {
        // For web, use bytes
        if (file.bytes != null) {
          request.files.add(http.MultipartFile.fromBytes(
            'file',
            file.bytes!,
            filename: file.name,
          ));
        } else {
          throw Exception('File data not available for web upload');
        }
      } else {
        // For mobile/desktop, use path
        if (file.path != null) {
          request.files.add(await http.MultipartFile.fromPath(
            'file',
            file.path!,
            filename: file.name,
          ));
        } else {
          throw Exception('File path not available for upload');
        }
      }

      // Add file type
      request.fields['fileType'] = fileType;

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final data = json.decode(responseBody);
        return data['fileUrl'] ?? data['url'] ?? '';
      } else {
        final errorData = json.decode(responseBody);
        throw Exception(errorData['message'] ?? 'Failed to upload file');
      }
    } catch (e) {
      throw Exception('Error uploading file: $e');
    }
  }

  // Upload CV specifically
  Future<String> uploadCV(File cvFile) async {
    // Validate file type
    if (!_isValidPDF(cvFile)) {
      throw Exception('Only PDF files are allowed for CV upload');
    }
    
    // Validate file size (max 5MB)
    if (!_isValidFileSize(cvFile, 5 * 1024 * 1024)) {
      throw Exception('CV file size must be less than 5MB');
    }
    
    return await uploadFile(cvFile, 'cv');
  }

  // Upload profile picture
  Future<String> uploadProfilePicture(File imageFile) async {
    // Validate file type
    if (!_isValidImage(imageFile)) {
      throw Exception('Only JPG, PNG, and GIF files are allowed for profile pictures');
    }
    
    // Validate file size (max 2MB)
    if (!_isValidFileSize(imageFile, 2 * 1024 * 1024)) {
      throw Exception('Profile picture size must be less than 2MB');
    }
    
    return await uploadFile(imageFile, 'profile_picture');
  }

  // Upload portfolio item
  Future<String> uploadPortfolioItem(File file) async {
    // Validate file size (max 10MB)
    if (!_isValidFileSize(file, 10 * 1024 * 1024)) {
      throw Exception('Portfolio file size must be less than 10MB');
    }
    
    return await uploadFile(file, 'portfolio');
  }

  // Delete uploaded file
  Future<bool> deleteFile(String fileUrl) async {
    try {
      final headers = await _headers;
      final response = await http.delete(
        Uri.parse('$baseUrl/delete'),
        headers: {
          ...headers,
          'Content-Type': 'application/json',
        },
        body: json.encode({'fileUrl': fileUrl}),
      );
      
      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error deleting file: $e');
    }
  }

  // Generate unique filename
  String _generateFileName(File file, String fileType) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = _getFileExtension(file);
    return '${fileType}_$timestamp$extension';
  }

  // Get file extension
  String _getFileExtension(File file) {
    final path = file.path;
    final lastDot = path.lastIndexOf('.');
    if (lastDot != -1) {
      return path.substring(lastDot);
    }
    return '';
  }

  // Validate PDF file
  bool _isValidPDF(File file) {
    final extension = _getFileExtension(file).toLowerCase();
    return extension == '.pdf';
  }

  // Validate image file
  bool _isValidImage(File file) {
    final extension = _getFileExtension(file).toLowerCase();
    return ['.jpg', '.jpeg', '.png', '.gif'].contains(extension);
  }

  // Validate file size
  bool _isValidFileSize(File file, int maxSizeBytes) {
    final fileSize = file.lengthSync();
    return fileSize <= maxSizeBytes;
  }

  // Get file size in human readable format
  String getFileSizeString(File file) {
    final bytes = file.lengthSync();
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  // Validate file before upload
  Map<String, dynamic> validateFile(File file, String fileType) {
    final result = <String, dynamic>{
      'isValid': true,
      'errors': <String>[],
    };

    switch (fileType) {
      case 'cv':
        if (!_isValidPDF(file)) {
          result['isValid'] = false;
          result['errors'].add('CV must be a PDF file');
        }
        if (!_isValidFileSize(file, 5 * 1024 * 1024)) {
          result['isValid'] = false;
          result['errors'].add('CV file size must be less than 5MB');
        }
        break;
      
      case 'profile_picture':
        if (!_isValidImage(file)) {
          result['isValid'] = false;
          result['errors'].add('Profile picture must be JPG, PNG, or GIF');
        }
        if (!_isValidFileSize(file, 2 * 1024 * 1024)) {
          result['isValid'] = false;
          result['errors'].add('Profile picture size must be less than 2MB');
        }
        break;
      
      case 'portfolio':
        if (!_isValidFileSize(file, 10 * 1024 * 1024)) {
          result['isValid'] = false;
          result['errors'].add('Portfolio file size must be less than 10MB');
        }
        break;
    }

    return result;
  }

  // Mock upload for development (returns a fake URL)
  Future<String> mockUpload(File file, String fileType) async {
    // Simulate upload delay
    await Future.delayed(const Duration(seconds: 2));

    // Validate file
    final validation = validateFile(file, fileType);
    if (!validation['isValid']) {
      throw Exception(validation['errors'].join(', '));
    }

    // Return mock URL
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'https://mock-storage.quickhire.com/${fileType}s/${timestamp}_${_generateFileName(file, fileType)}';
  }

  // Mock upload for PlatformFile (for development)
  Future<String> mockUploadPlatformFile(PlatformFile file, String fileType) async {
    // Simulate upload delay
    await Future.delayed(const Duration(seconds: 1));

    // Basic validation for PlatformFile
    if (fileType == 'cv' && !file.name.toLowerCase().endsWith('.pdf')) {
      throw Exception('CV must be a PDF file');
    }

    // Check file size
    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      throw Exception('File size must be less than 5MB');
    }

    // For web, ensure we have file data
    if (kIsWeb && file.bytes == null) {
      throw Exception('File data not available. Please try selecting the file again.');
    }

    // Return mock URL
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'https://mock-storage.quickhire.com/${fileType}s/${timestamp}_${file.name}';
  }

  // Get supported file types for each category
  Map<String, List<String>> getSupportedFileTypes() {
    return {
      'cv': ['.pdf'],
      'profile_picture': ['.jpg', '.jpeg', '.png', '.gif'],
      'portfolio': ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.doc', '.docx'],
    };
  }

  // Get max file sizes for each category (in bytes)
  Map<String, int> getMaxFileSizes() {
    return {
      'cv': 5 * 1024 * 1024, // 5MB
      'profile_picture': 2 * 1024 * 1024, // 2MB
      'portfolio': 10 * 1024 * 1024, // 10MB
    };
  }
}
