// models/Project.js
const mongoose = require('mongoose');

const ProjectSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a project title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [2000, 'Description cannot be more than 2000 characters']
  },
  skills: {
    type: [String],
    required: [true, 'Please add required skills']
  },
  location: {
    type: String,
    required: [true, 'Please add a location']
  },
  workType: {
    type: String,
    enum: ['remote', 'onsite', 'hybrid'],
    default: 'remote'
  },
  // Optional GeoJSON location data for more precise location matching
//   geoLocation: {
//     type: {
//       type: String,
//       enum: ['Point'],
//     },
//     coordinates: {
//       type: [Number],
//       index: '2dsphere'
//     }
//   },
  budget: {
    min: {
      type: Number,
      required: [true, 'Please add minimum budget']
    },
    max: {
      type: Number,
      required: [true, 'Please add maximum budget']
    },
    currency: {
      type: String,
      default: 'USD',
      enum: ['USD', 'EUR', 'GBP', 'PKR', 'INR']
    }
  },
  duration: {
    type: String,
    required: [true, 'Please specify project duration']
  },
  deadline: {
    type: Date,
    required: [true, 'Please specify project deadline']
  },
  experienceLevel: {
    type: String,
    enum: ['entry', 'intermediate', 'expert'],
    required: [true, 'Please specify required experience level']
  },
  projectType: {
    type: String,
    enum: ['fixed-price', 'hourly'],
    default: 'fixed-price'
  },
  attachments: [{
    filename: String,
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  employer: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['open', 'in-progress', 'completed', 'cancelled'],
    default: 'open'
  },
  // Store job seekers who have applied to the project
  applications: [
    {
      jobSeeker: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: true
      },
      status: {
        type: String,
        enum: ['pending', 'accepted', 'rejected', 'withdrawn'],
        default: 'pending'
      },
      appliedAt: {
        type: Date,
        default: Date.now
      },
      coverLetter: {
        type: String,
        maxlength: [1000, 'Cover letter cannot exceed 1000 characters']
      },
      proposedBudget: {
        type: Number
      },
      estimatedDuration: {
        type: String
      },
      portfolio: [{
        title: String,
        description: String,
        url: String
      }],
      reviewedAt: {
        type: Date
      },
      reviewedBy: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      }
    }
  ],
  // Selected job seeker for the project
  selectedJobSeeker: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },
  selectedAt: {
    type: Date
  },
  // Store job seekers who swiped right (interested)
  interestedUsers: [
    {
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: true
      },
      swipedAt: {
        type: Date,
        default: Date.now
      }
    }
  ],
  // Project milestones
  milestones: [{
    title: {
      type: String,
      required: true
    },
    description: String,
    dueDate: Date,
    status: {
      type: String,
      enum: ['pending', 'in-progress', 'completed'],
      default: 'pending'
    },
    completedAt: Date
  }],
  // Project completion details
  completedAt: {
    type: Date
  },
  actualBudget: {
    type: Number
  },
  // Project visibility
  isPublic: {
    type: Boolean,
    default: true
  },
  // Featured project
  isFeatured: {
    type: Boolean,
    default: false
  },
  // Project views count
  viewsCount: {
    type: Number,
    default: 0
  },
  // Last activity timestamp
  lastActivity: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Middleware to update timestamps
ProjectSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  this.lastActivity = Date.now();
  next();
});

ProjectSchema.pre('findOneAndUpdate', function(next) {
  this.set({ updatedAt: Date.now(), lastActivity: Date.now() });
  next();
});

// Create indexes for better query performance
ProjectSchema.index({ skills: 1 });
ProjectSchema.index({ location: 'text' });
ProjectSchema.index({ status: 1 });
ProjectSchema.index({ employer: 1 });
ProjectSchema.index({ experienceLevel: 1 });
ProjectSchema.index({ projectType: 1 });
ProjectSchema.index({ deadline: 1 });
ProjectSchema.index({ createdAt: -1 });
ProjectSchema.index({ lastActivity: -1 });
ProjectSchema.index({ 'budget.min': 1, 'budget.max': 1 });

// Compound indexes
ProjectSchema.index({ status: 1, deadline: 1 });
ProjectSchema.index({ employer: 1, status: 1 });
ProjectSchema.index({ skills: 1, experienceLevel: 1 });

module.exports = mongoose.model('Project', ProjectSchema);
