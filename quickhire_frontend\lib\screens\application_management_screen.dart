// lib/screens/application_management_screen.dart
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/application_model.dart';
import '../models/job_listing.dart';
import '../services/application_service.dart';

class ApplicationManagementScreen extends StatefulWidget {
  static const String routeName = '/application-management';
  static const String id = 'ApplicationManagementScreen';

  final JobListing project;

  const ApplicationManagementScreen({
    Key? key,
    required this.project,
  }) : super(key: key);

  @override
  State<ApplicationManagementScreen> createState() => _ApplicationManagementScreenState();
}

class _ApplicationManagementScreenState extends State<ApplicationManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ApplicationService _applicationService = ApplicationService();
  
  List<DetailedApplication> _allApplications = [];
  List<DetailedApplication> _pendingApplications = [];
  List<DetailedApplication> _acceptedApplications = [];
  List<DetailedApplication> _rejectedApplications = [];
  ApplicationStats? _stats;
  
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadApplications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadApplications() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // For development, use mock data
      final applications = _applicationService.getMockApplications();
      final stats = await _applicationService.getApplicationStats(widget.project.id);
      
      setState(() {
        _allApplications = applications;
        _pendingApplications = applications.where((app) => app.isPending).toList();
        _acceptedApplications = applications.where((app) => app.isAccepted).toList();
        _rejectedApplications = applications.where((app) => app.isRejected).toList();
        _stats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _updateApplicationStatus(String applicationId, String status) async {
    try {
      await _applicationService.updateApplicationStatus(
        widget.project.id,
        applicationId,
        status,
      );
      
      _showSuccessSnackBar('Application $status successfully');
      await _loadApplications(); // Reload applications
    } catch (e) {
      _showErrorSnackBar('Failed to update application: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Applications'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              text: 'All (${_allApplications.length})',
              icon: const Icon(Icons.list),
            ),
            Tab(
              text: 'Pending (${_pendingApplications.length})',
              icon: const Icon(Icons.pending),
            ),
            Tab(
              text: 'Accepted (${_acceptedApplications.length})',
              icon: const Icon(Icons.check_circle),
            ),
            Tab(
              text: 'Rejected (${_rejectedApplications.length})',
              icon: const Icon(Icons.cancel),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Project info header
          _buildProjectHeader(),
          // Statistics section
          if (_stats != null) _buildStatsSection(),
          // Applications list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator(color: Colors.amber))
                : _error != null
                    ? _buildErrorWidget()
                    : TabBarView(
                        controller: _tabController,
                        children: [
                          _buildApplicationsList(_allApplications),
                          _buildApplicationsList(_pendingApplications),
                          _buildApplicationsList(_acceptedApplications),
                          _buildApplicationsList(_rejectedApplications),
                        ],
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.project.title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.project.description,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.attach_money, size: 16, color: Colors.grey[600]),
              Text(
                '${widget.project.budget.currency} ${widget.project.budget.min.toStringAsFixed(0)} - ${widget.project.budget.max.toStringAsFixed(0)}',
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(width: 16),
              Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
              Text(
                widget.project.duration,
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'Acceptance Rate',
              '${_stats!.acceptanceRate.toStringAsFixed(1)}%',
              Icons.trending_up,
              Colors.green,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'Avg. Budget',
              '${widget.project.budget.currency} ${_stats!.averageProposedBudget.toStringAsFixed(0)}',
              Icons.attach_money,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'Top Level',
              _stats!.mostCommonExperienceLevel.toUpperCase(),
              Icons.star,
              Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: const TextStyle(
              fontSize: 10,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading applications',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadApplications,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildApplicationsList(List<DetailedApplication> applications) {
    if (applications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No applications found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadApplications,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: applications.length,
        itemBuilder: (context, index) {
          return _buildApplicationCard(applications[index]);
        },
      ),
    );
  }

  Widget _buildApplicationCard(DetailedApplication application) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with job seeker info and status
            Row(
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundColor: Colors.grey[300],
                  child: Text(
                    application.jobSeeker.name.substring(0, 1).toUpperCase(),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        application.jobSeeker.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.location_on, size: 14, color: Colors.grey[600]),
                          const SizedBox(width: 4),
                          Text(
                            application.jobSeeker.location,
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                          const SizedBox(width: 12),
                          if (application.jobSeeker.rating != null) ...[
                            Icon(Icons.star, size: 14, color: Colors.amber),
                            const SizedBox(width: 4),
                            Text(
                              application.jobSeeker.rating!.toStringAsFixed(1),
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(application.status),
              ],
            ),
            const SizedBox(height: 16),

            // Experience and skills
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    application.jobSeeker.experienceLevel.toUpperCase(),
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${application.jobSeeker.completedProjects} projects completed',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Skills
            Wrap(
              spacing: 6,
              runSpacing: 6,
              children: application.jobSeeker.skills.take(4).map((skill) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    skill,
                    style: const TextStyle(fontSize: 12),
                  ),
                );
              }).toList(),
            ),

            const SizedBox(height: 16),

            // Action buttons for pending applications
            if (application.isPending)
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _updateApplicationStatus(application.id, 'rejected'),
                      icon: const Icon(Icons.close, size: 18),
                      label: const Text('Reject'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: const BorderSide(color: Colors.red),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _updateApplicationStatus(application.id, 'accepted'),
                      icon: const Icon(Icons.check, size: 18),
                      label: const Text('Accept'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),

            // View details button
            const SizedBox(height: 8),
            Center(
              child: TextButton(
                onPressed: () => _showApplicationDetails(application),
                child: const Text('View Full Details'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    IconData icon;

    switch (status) {
      case 'pending':
        color = Colors.orange;
        icon = Icons.pending;
        break;
      case 'accepted':
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case 'rejected':
        color = Colors.red;
        icon = Icons.cancel;
        break;
      case 'withdrawn':
        color = Colors.grey;
        icon = Icons.remove_circle;
        break;
      default:
        color = Colors.grey;
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            status.toUpperCase(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _showApplicationDetails(DetailedApplication application) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(application.jobSeeker.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Email: ${application.jobSeeker.email}'),
              const SizedBox(height: 8),
              Text('Location: ${application.jobSeeker.location}'),
              const SizedBox(height: 8),
              Text('Experience: ${application.jobSeeker.experienceLevel}'),
              const SizedBox(height: 8),
              if (application.jobSeeker.bio != null) ...[
                const Text('Bio:', style: TextStyle(fontWeight: FontWeight.bold)),
                Text(application.jobSeeker.bio!),
                const SizedBox(height: 8),
              ],
              if (application.coverLetter != null) ...[
                const Text('Cover Letter:', style: TextStyle(fontWeight: FontWeight.bold)),
                Text(application.coverLetter!),
                const SizedBox(height: 8),
              ],
              if (application.proposedBudget != null)
                Text('Proposed Budget: ${widget.project.budget.currency} ${application.proposedBudget!.toStringAsFixed(0)}'),
              if (application.estimatedDuration != null)
                Text('Estimated Duration: ${application.estimatedDuration}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (application.isPending) ...[
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _updateApplicationStatus(application.id, 'rejected');
              },
              child: const Text('Reject', style: TextStyle(color: Colors.red)),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _updateApplicationStatus(application.id, 'accepted');
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
              child: const Text('Accept', style: TextStyle(color: Colors.white)),
            ),
          ],
        ],
      ),
    );
  }
}