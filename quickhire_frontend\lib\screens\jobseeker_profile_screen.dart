// lib/screens/jobseeker_profile_screen.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../models/jobseeker_model.dart';
import '../services/jobseeker_service.dart';
import '../services/file_upload_service.dart';

class JobSeekerProfileScreen extends StatefulWidget {
  static const String routeName = '/jobseeker-profile';
  static const String id = 'JobSeekerProfileScreen';

  const JobSeekerProfileScreen({Key? key}) : super(key: key);

  @override
  State<JobSeekerProfileScreen> createState() => _JobSeekerProfileScreenState();
}

class _JobSeekerProfileScreenState extends State<JobSeekerProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final JobSeekerService _jobSeekerService = JobSeekerService();
  final FileUploadService _fileUploadService = FileUploadService();
  final ImagePicker _imagePicker = ImagePicker();

  // Controllers
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _locationController = TextEditingController();
  final _bioController = TextEditingController();

  // State
  JobSeekerModel? _jobSeeker;
  bool _isLoading = true;
  bool _isEditing = false;
  bool _isSaving = false;
  List<String> _selectedSkills = [];
  String _experienceLevel = 'entry';
  File? _newProfilePicture;
  File? _newCV;
  String? _newCVFileName;

  // Options
  final List<String> _experienceLevels = ['entry', 'intermediate', 'expert'];
  final List<String> _skillOptions = [
    'Flutter', 'React', 'Node.js', 'Python', 'Java', 'JavaScript',
    'UI/UX Design', 'Graphic Design', 'Digital Marketing', 'Content Writing',
    'Data Analysis', 'Machine Learning', 'DevOps', 'Project Management',
    'Mobile Development', 'Web Development', 'Database Management',
    'Cloud Computing', 'Cybersecurity', 'Quality Assurance'
  ];

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _locationController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  Future<void> _loadProfile() async {
    try {
      final jobSeeker = await _jobSeekerService.getProfile();
      setState(() {
        _jobSeeker = jobSeeker;
        _nameController.text = jobSeeker.name;
        _emailController.text = jobSeeker.email;
        _phoneController.text = jobSeeker.phone ?? '';
        _locationController.text = jobSeeker.location;
        _bioController.text = jobSeeker.bio ?? '';
        _selectedSkills = List.from(jobSeeker.skills);
        _experienceLevel = jobSeeker.experienceLevel;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showError('Failed to load profile: $e');
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
    });

    try {
      String? profilePictureUrl = _jobSeeker?.profilePicture;
      String? cvUrl = _jobSeeker?.cvUrl;

      // Upload new profile picture if selected
      if (_newProfilePicture != null) {
        profilePictureUrl = await _fileUploadService.uploadProfilePicture(_newProfilePicture!);
      }

      // Upload new CV if selected
      if (_newCV != null) {
        cvUrl = await _fileUploadService.uploadCV(_newCV!);
      }

      final updatedJobSeeker = _jobSeeker!.copyWith(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        location: _locationController.text.trim(),
        bio: _bioController.text.trim().isEmpty ? null : _bioController.text.trim(),
        skills: _selectedSkills,
        experienceLevel: _experienceLevel,
        profilePicture: profilePictureUrl,
        cvUrl: cvUrl,
        updatedAt: DateTime.now(),
      );

      final savedJobSeeker = await _jobSeekerService.updateProfile(updatedJobSeeker);

      setState(() {
        _jobSeeker = savedJobSeeker;
        _isEditing = false;
        _newProfilePicture = null;
        _newCV = null;
        _newCVFileName = null;
      });

      _showSuccess('Profile updated successfully!');
    } catch (e) {
      _showError('Failed to update profile: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Future<void> _pickProfilePicture() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _newProfilePicture = File(image.path);
        });
      }
    } catch (e) {
      _showError('Error picking profile picture: $e');
    }
  }

  Future<void> _pickCV() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _newCV = File(result.files.single.path!);
          _newCVFileName = result.files.single.name;
        });
      }
    } catch (e) {
      _showError('Error picking CV file: $e');
    }
  }

  void _toggleSkill(String skill) {
    setState(() {
      if (_selectedSkills.contains(skill)) {
        _selectedSkills.remove(skill);
      } else {
        _selectedSkills.add(skill);
      }
    });
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('My Profile'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: CircularProgressIndicator(color: Colors.blue),
        ),
      );
    }

    if (_jobSeeker == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('My Profile'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Text('Failed to load profile'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Profile'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          if (!_isEditing)
            IconButton(
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
              icon: const Icon(Icons.edit),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile completion indicator
              _buildProfileCompletionCard(),
              const SizedBox(height: 24),

              // Profile picture section
              _buildProfilePictureSection(),
              const SizedBox(height: 24),

              // Basic information
              _buildBasicInfoSection(),
              const SizedBox(height: 24),

              // Skills and experience
              _buildSkillsSection(),
              const SizedBox(height: 24),

              // CV section
              _buildCVSection(),
              const SizedBox(height: 24),

              // Bio section
              _buildBioSection(),
              const SizedBox(height: 24),

              // Action buttons
              if (_isEditing) _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileCompletionCard() {
    final completionPercentage = _jobSeeker!.profileCompletionPercentage;
    final missingFields = _jobSeeker!.missingProfileFields;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  completionPercentage == 100 ? Icons.check_circle : Icons.info,
                  color: completionPercentage == 100 ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Profile Completion: ${completionPercentage.toInt()}%',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: completionPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                completionPercentage == 100 ? Colors.green : Colors.orange,
              ),
            ),
            if (missingFields.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Missing: ${missingFields.join(', ')}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProfilePictureSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Profile Picture',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Center(
              child: Stack(
                children: [
                  CircleAvatar(
                    radius: 60,
                    backgroundColor: Colors.grey[300],
                    backgroundImage: _newProfilePicture != null
                        ? FileImage(_newProfilePicture!)
                        : (_jobSeeker!.hasProfilePicture
                            ? NetworkImage(_jobSeeker!.profilePicture!)
                            : null) as ImageProvider?,
                    child: (_newProfilePicture == null && !_jobSeeker!.hasProfilePicture)
                        ? Text(
                            _jobSeeker!.name.substring(0, 1).toUpperCase(),
                            style: const TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  if (_isEditing)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        decoration: const BoxDecoration(
                          color: Colors.blue,
                          shape: BoxShape.circle,
                        ),
                        child: IconButton(
                          onPressed: _pickProfilePicture,
                          icon: const Icon(Icons.camera_alt, color: Colors.white),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Basic Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Full Name',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              enabled: _isEditing,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Name is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email Address',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              enabled: false, // Email should not be editable
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone),
              ),
              enabled: _isEditing,
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _locationController,
              decoration: const InputDecoration(
                labelText: 'Location',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.location_on),
              ),
              enabled: _isEditing,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Location is required';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkillsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Skills & Experience',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Experience Level',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            if (_isEditing)
              ...List.generate(_experienceLevels.length, (index) {
                final level = _experienceLevels[index];
                return RadioListTile<String>(
                  title: Text(level.toUpperCase()),
                  value: level,
                  groupValue: _experienceLevel,
                  onChanged: (value) {
                    setState(() {
                      _experienceLevel = value!;
                    });
                  },
                );
              })
            else
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(_jobSeeker!.experienceLevelDisplay),
              ),
            const SizedBox(height: 16),
            const Text(
              'Skills',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            if (_isEditing)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _skillOptions.map((skill) {
                  final isSelected = _selectedSkills.contains(skill);
                  return FilterChip(
                    label: Text(skill),
                    selected: isSelected,
                    onSelected: (_) => _toggleSkill(skill),
                    selectedColor: Colors.blue.withValues(alpha: 0.3),
                    checkmarkColor: Colors.blue[800],
                  );
                }).toList(),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _jobSeeker!.skills.map((skill) {
                  return Chip(
                    label: Text(skill),
                    backgroundColor: Colors.blue.withValues(alpha: 0.1),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCVSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'CV/Resume',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (_isEditing)
              InkWell(
                onTap: _pickCV,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.upload_file, size: 32),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _newCV != null ? 'New CV Selected' : 'Update CV',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (_newCVFileName != null)
                              Text(
                                _newCVFileName!,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              )
                            else if (_jobSeeker!.hasCV)
                              const Text(
                                'Current CV will be replaced',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      _jobSeeker!.hasCV ? Icons.description : Icons.warning,
                      color: _jobSeeker!.hasCV ? Colors.green : Colors.orange,
                      size: 32,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        _jobSeeker!.hasCV ? 'CV uploaded' : 'No CV uploaded',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    if (_jobSeeker!.hasCV)
                      IconButton(
                        onPressed: () {
                          // Open CV in browser or download
                        },
                        icon: const Icon(Icons.open_in_new),
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBioSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Professional Bio',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _bioController,
              decoration: const InputDecoration(
                labelText: 'Tell employers about yourself',
                hintText: 'Describe your experience, skills, and what makes you unique...',
                border: OutlineInputBorder(),
              ),
              enabled: _isEditing,
              maxLines: 4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isSaving ? null : () {
              setState(() {
                _isEditing = false;
                // Reset form to original values
                _nameController.text = _jobSeeker!.name;
                _phoneController.text = _jobSeeker!.phone ?? '';
                _locationController.text = _jobSeeker!.location;
                _bioController.text = _jobSeeker!.bio ?? '';
                _selectedSkills = List.from(_jobSeeker!.skills);
                _experienceLevel = _jobSeeker!.experienceLevel;
                _newProfilePicture = null;
                _newCV = null;
                _newCVFileName = null;
              });
            },
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isSaving ? null : _saveProfile,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: _isSaving
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('Save Changes'),
          ),
        ),
      ],
    );
  }
}
