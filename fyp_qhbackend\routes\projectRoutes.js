// routes/projectRoutes.js
const express = require('express');
const {
  createProject,
  getProjects,
  getProject,
  updateProject,
  deleteProject,
  getMatchedProjects,
  acceptProject,
  applyToProject,
  getProjectApplicants,
  getProjectApplications,
  updateApplicantStatus,
  updateApplicationStatus,
  completeProject,
  getEmployerProjects,
  getAcceptedProjects
} = require('../controllers/projectController');

const { protect, authorize } = require('../middlewares/auth');

const router = express.Router();

// Public routes
router.get('/', getProjects);
router.get('/:id', getProject);

// Protected routes - Project CRUD
router.post('/', protect, authorize('employer'), createProject);
router.put('/:id', protect, authorize('employer'), updateProject);
router.delete('/:id', protect, authorize('employer'), deleteProject);

// Project status management
router.put('/:id/complete', protect, authorize('employer'), completeProject);

// Job seeker routes
router.get('/matches/find', protect, authorize('jobseeker'), getMatchedProjects);
router.post('/:id/accept', protect, authorize('jobseeker'), acceptProject);
router.post('/:id/apply', protect, authorize('jobseeker'), applyToProject);

// Application management routes
router.get('/:id/applicants', protect, authorize('employer'), getProjectApplicants);
router.get('/:id/applications', protect, authorize('employer'), getProjectApplications);
router.put('/:id/applicants/:applicantId', protect, authorize('employer'), updateApplicantStatus);
router.put('/:id/applications/:applicationId', protect, authorize('employer'), updateApplicationStatus);

// User-specific project lists
router.get('/employer/list', protect, authorize('employer'), getEmployerProjects);
router.get('/jobseeker/accepted', protect, authorize('jobseeker'), getAcceptedProjects);

module.exports = router;