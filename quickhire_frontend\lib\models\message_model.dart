// lib/models/message_model.dart

enum MessageType {
  text,
  image,
  file,
  meeting,
  system
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed
}

class MessageAttachment {
  final String id;
  final String name;
  final String url;
  final String type; // image, document, etc.
  final int size; // in bytes
  final DateTime uploadedAt;

  MessageAttachment({
    required this.id,
    required this.name,
    required this.url,
    required this.type,
    required this.size,
    required this.uploadedAt,
  });

  factory MessageAttachment.fromJson(Map<String, dynamic> json) {
    return MessageAttachment(
      id: json['id'] ?? json['_id'] ?? '',
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      type: json['type'] ?? '',
      size: json['size'] ?? 0,
      uploadedAt: json['uploadedAt'] != null 
          ? DateTime.parse(json['uploadedAt']) 
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'type': type,
      'size': size,
      'uploadedAt': uploadedAt.toIso8601String(),
    };
  }

  String get sizeFormatted {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  bool get isImage => type.startsWith('image/');
  bool get isDocument => type.contains('pdf') || type.contains('doc') || type.contains('txt');
}

class Meeting {
  final String id;
  final String title;
  final String description;
  final DateTime scheduledAt;
  final int durationMinutes;
  final String? meetingUrl;
  final String? meetingId;
  final String? password;
  final String status; // scheduled, ongoing, completed, cancelled
  final String organizer;
  final List<String> participants;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Meeting({
    required this.id,
    required this.title,
    required this.description,
    required this.scheduledAt,
    required this.durationMinutes,
    this.meetingUrl,
    this.meetingId,
    this.password,
    required this.status,
    required this.organizer,
    required this.participants,
    required this.createdAt,
    this.updatedAt,
  });

  factory Meeting.fromJson(Map<String, dynamic> json) {
    return Meeting(
      id: json['id'] ?? json['_id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      scheduledAt: json['scheduledAt'] != null 
          ? DateTime.parse(json['scheduledAt']) 
          : DateTime.now(),
      durationMinutes: json['durationMinutes'] ?? 30,
      meetingUrl: json['meetingUrl'],
      meetingId: json['meetingId'],
      password: json['password'],
      status: json['status'] ?? 'scheduled',
      organizer: json['organizer'] ?? '',
      participants: json['participants'] != null 
          ? List<String>.from(json['participants']) 
          : [],
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'scheduledAt': scheduledAt.toIso8601String(),
      'durationMinutes': durationMinutes,
      'meetingUrl': meetingUrl,
      'meetingId': meetingId,
      'password': password,
      'status': status,
      'organizer': organizer,
      'participants': participants,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  bool get isUpcoming => scheduledAt.isAfter(DateTime.now()) && status == 'scheduled';
  bool get isOngoing => status == 'ongoing';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';

  DateTime get endTime => scheduledAt.add(Duration(minutes: durationMinutes));
}

class EnhancedMessage {
  final String id;
  final String senderId;
  final String senderName;
  final String? senderAvatar;
  final String receiverId;
  final String content;
  final MessageType type;
  final MessageStatus status;
  final DateTime timestamp;
  final DateTime? readAt;
  final DateTime? editedAt;
  final List<MessageAttachment> attachments;
  final Meeting? meeting;
  final String? replyToId;
  final EnhancedMessage? replyToMessage;

  EnhancedMessage({
    required this.id,
    required this.senderId,
    required this.senderName,
    this.senderAvatar,
    required this.receiverId,
    required this.content,
    required this.type,
    required this.status,
    required this.timestamp,
    this.readAt,
    this.editedAt,
    this.attachments = const [],
    this.meeting,
    this.replyToId,
    this.replyToMessage,
  });

  factory EnhancedMessage.fromJson(Map<String, dynamic> json) {
    return EnhancedMessage(
      id: json['id'] ?? json['_id'] ?? '',
      senderId: json['senderId'] ?? json['sender'] ?? '',
      senderName: json['senderName'] ?? '',
      senderAvatar: json['senderAvatar'],
      receiverId: json['receiverId'] ?? json['receiver'] ?? '',
      content: json['content'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.toString().split('.').last == (json['type'] ?? 'text'),
        orElse: () => MessageType.text,
      ),
      status: MessageStatus.values.firstWhere(
        (e) => e.toString().split('.').last == (json['status'] ?? 'sent'),
        orElse: () => MessageStatus.sent,
      ),
      timestamp: json['timestamp'] != null 
          ? DateTime.parse(json['timestamp']) 
          : DateTime.now(),
      readAt: json['readAt'] != null 
          ? DateTime.parse(json['readAt']) 
          : null,
      editedAt: json['editedAt'] != null 
          ? DateTime.parse(json['editedAt']) 
          : null,
      attachments: json['attachments'] != null 
          ? (json['attachments'] as List).map((a) => MessageAttachment.fromJson(a)).toList()
          : [],
      meeting: json['meeting'] != null 
          ? Meeting.fromJson(json['meeting']) 
          : null,
      replyToId: json['replyToId'],
      replyToMessage: json['replyToMessage'] != null 
          ? EnhancedMessage.fromJson(json['replyToMessage']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'senderAvatar': senderAvatar,
      'receiverId': receiverId,
      'content': content,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'timestamp': timestamp.toIso8601String(),
      'readAt': readAt?.toIso8601String(),
      'editedAt': editedAt?.toIso8601String(),
      'attachments': attachments.map((a) => a.toJson()).toList(),
      'meeting': meeting?.toJson(),
      'replyToId': replyToId,
      'replyToMessage': replyToMessage?.toJson(),
    };
  }

  bool get isRead => readAt != null;
  bool get isEdited => editedAt != null;
  bool get hasAttachments => attachments.isNotEmpty;
  bool get hasMeeting => meeting != null;
  bool get isReply => replyToId != null;

  EnhancedMessage copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? senderAvatar,
    String? receiverId,
    String? content,
    MessageType? type,
    MessageStatus? status,
    DateTime? timestamp,
    DateTime? readAt,
    DateTime? editedAt,
    List<MessageAttachment>? attachments,
    Meeting? meeting,
    String? replyToId,
    EnhancedMessage? replyToMessage,
  }) {
    return EnhancedMessage(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderAvatar: senderAvatar ?? this.senderAvatar,
      receiverId: receiverId ?? this.receiverId,
      content: content ?? this.content,
      type: type ?? this.type,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      readAt: readAt ?? this.readAt,
      editedAt: editedAt ?? this.editedAt,
      attachments: attachments ?? this.attachments,
      meeting: meeting ?? this.meeting,
      replyToId: replyToId ?? this.replyToId,
      replyToMessage: replyToMessage ?? this.replyToMessage,
    );
  }
}

class ChatConversation {
  final String id;
  final String projectId;
  final String projectTitle;
  final String employerId;
  final String employerName;
  final String? employerAvatar;
  final String jobSeekerId;
  final String jobSeekerName;
  final String? jobSeekerAvatar;
  final EnhancedMessage? lastMessage;
  final int unreadCount;
  final DateTime lastActivity;
  final bool isActive;

  ChatConversation({
    required this.id,
    required this.projectId,
    required this.projectTitle,
    required this.employerId,
    required this.employerName,
    this.employerAvatar,
    required this.jobSeekerId,
    required this.jobSeekerName,
    this.jobSeekerAvatar,
    this.lastMessage,
    required this.unreadCount,
    required this.lastActivity,
    required this.isActive,
  });

  factory ChatConversation.fromJson(Map<String, dynamic> json) {
    return ChatConversation(
      id: json['id'] ?? json['_id'] ?? '',
      projectId: json['projectId'] ?? '',
      projectTitle: json['projectTitle'] ?? '',
      employerId: json['employerId'] ?? '',
      employerName: json['employerName'] ?? '',
      employerAvatar: json['employerAvatar'],
      jobSeekerId: json['jobSeekerId'] ?? '',
      jobSeekerName: json['jobSeekerName'] ?? '',
      jobSeekerAvatar: json['jobSeekerAvatar'],
      lastMessage: json['lastMessage'] != null 
          ? EnhancedMessage.fromJson(json['lastMessage']) 
          : null,
      unreadCount: json['unreadCount'] ?? 0,
      lastActivity: json['lastActivity'] != null 
          ? DateTime.parse(json['lastActivity']) 
          : DateTime.now(),
      isActive: json['isActive'] ?? true,
    );
  }

  String getOtherUserName(String currentUserId) {
    return currentUserId == employerId ? jobSeekerName : employerName;
  }

  String? getOtherUserAvatar(String currentUserId) {
    return currentUserId == employerId ? jobSeekerAvatar : employerAvatar;
  }

  String getOtherUserId(String currentUserId) {
    return currentUserId == employerId ? jobSeekerId : employerId;
  }
}
