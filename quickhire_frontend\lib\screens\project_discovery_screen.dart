// lib/screens/project_discovery_screen.dart
import 'package:flutter/material.dart';
import '../models/job_listing.dart';
import '../services/jobseeker_service.dart';
import '../widgets/project_card_swipeable.dart';
import 'project_application_screen.dart';
import 'project_filters_screen.dart';

class ProjectDiscoveryScreen extends StatefulWidget {
  static const String routeName = '/project-discovery';
  static const String id = 'ProjectDiscoveryScreen';

  const ProjectDiscoveryScreen({Key? key}) : super(key: key);

  @override
  State<ProjectDiscoveryScreen> createState() => _ProjectDiscoveryScreenState();
}

class _ProjectDiscoveryScreenState extends State<ProjectDiscoveryScreen> {
  final JobSeekerService _jobSeekerService = JobSeekerService();
  final PageController _pageController = PageController();

  List<JobListing> _projects = [];
  List<JobListing> _filteredProjects = [];
  int _currentIndex = 0;
  bool _isLoading = true;
  bool _hasMoreProjects = true;

  // Filter state
  ProjectFilters _filters = ProjectFilters();

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadProjects() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // For now, use mock data. In production, this would call the API
      final projects = _jobSeekerService.getMockProjects();
      
      setState(() {
        _projects = projects;
        _filteredProjects = _applyFilters(projects);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showError('Failed to load projects: $e');
    }
  }

  List<JobListing> _applyFilters(List<JobListing> projects) {
    return projects.where((project) {
      // Location filter
      if (_filters.location != null && _filters.location!.isNotEmpty) {
        if (!project.location.toLowerCase().contains(_filters.location!.toLowerCase())) {
          return false;
        }
      }

      // Budget filter
      if (_filters.minBudget != null) {
        if (project.budget.min < _filters.minBudget!) return false;
      }
      if (_filters.maxBudget != null) {
        if (project.budget.max > _filters.maxBudget!) return false;
      }

      // Experience level filter
      if (_filters.experienceLevel != null && _filters.experienceLevel!.isNotEmpty) {
        if (project.experienceLevel != _filters.experienceLevel) return false;
      }

      // Skills filter
      if (_filters.skills != null && _filters.skills!.isNotEmpty) {
        final hasMatchingSkill = _filters.skills!.any((skill) =>
            project.skills.any((projectSkill) =>
                projectSkill.toLowerCase().contains(skill.toLowerCase())));
        if (!hasMatchingSkill) return false;
      }

      return true;
    }).toList();
  }

  void _onSwipeLeft() {
    // Swipe left = pass/skip project
    _nextProject();
  }

  void _onSwipeRight() {
    // Swipe right = apply to project
    if (_currentIndex < _filteredProjects.length) {
      final project = _filteredProjects[_currentIndex];
      _showApplicationDialog(project);
    }
  }

  void _nextProject() {
    if (_currentIndex < _filteredProjects.length - 1) {
      setState(() {
        _currentIndex++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // No more projects
      _showNoMoreProjectsDialog();
    }
  }

  void _showApplicationDialog(JobListing project) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Apply to Project'),
        content: Text('Do you want to apply to "${project.title}"?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _nextProject();
            },
            child: const Text('Skip'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToApplication(project);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _navigateToApplication(JobListing project) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectApplicationScreen(project: project),
      ),
    ).then((applied) {
      if (applied == true) {
        _showSuccess('Application submitted successfully!');
      }
      _nextProject();
    });
  }

  void _showNoMoreProjectsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('No More Projects'),
        content: const Text('You\'ve seen all available projects. Check back later for new opportunities!'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _loadProjects(); // Reload projects
            },
            child: const Text('Refresh'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Go back to main screen
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _openFilters() async {
    final newFilters = await Navigator.push<ProjectFilters>(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectFiltersScreen(currentFilters: _filters),
      ),
    );

    if (newFilters != null) {
      setState(() {
        _filters = newFilters;
        _filteredProjects = _applyFilters(_projects);
        _currentIndex = 0;
      });
      
      if (_filteredProjects.isNotEmpty) {
        _pageController.animateToPage(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Discover Projects'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _openFilters,
            icon: Stack(
              children: [
                const Icon(Icons.filter_list),
                if (_filters.hasActiveFilters)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: _loadProjects,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.blue),
      );
    }

    if (_filteredProjects.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No projects found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your filters or check back later',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _openFilters,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Adjust Filters'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Progress indicator
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Text(
                '${_currentIndex + 1} of ${_filteredProjects.length}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: LinearProgressIndicator(
                  value: (_currentIndex + 1) / _filteredProjects.length,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              ),
            ],
          ),
        ),
        // Project cards
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            itemCount: _filteredProjects.length,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemBuilder: (context, index) {
              final project = _filteredProjects[index];
              return Padding(
                padding: const EdgeInsets.all(16),
                child: ProjectCardSwipeable(
                  project: project,
                  onSwipeLeft: _onSwipeLeft,
                  onSwipeRight: _onSwipeRight,
                ),
              );
            },
          ),
        ),
        // Action buttons
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Pass button
          FloatingActionButton(
            onPressed: _onSwipeLeft,
            backgroundColor: Colors.red,
            heroTag: 'pass',
            child: const Icon(Icons.close, color: Colors.white),
          ),
          // Info button
          FloatingActionButton(
            onPressed: () {
              if (_currentIndex < _filteredProjects.length) {
                _showProjectDetails(_filteredProjects[_currentIndex]);
              }
            },
            backgroundColor: Colors.grey,
            heroTag: 'info',
            child: const Icon(Icons.info, color: Colors.white),
          ),
          // Apply button
          FloatingActionButton(
            onPressed: _onSwipeRight,
            backgroundColor: Colors.green,
            heroTag: 'apply',
            child: const Icon(Icons.favorite, color: Colors.white),
          ),
        ],
      ),
    );
  }

  void _showProjectDetails(JobListing project) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  project.title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'by ${project.employerName}',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Description',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(project.description),
                const SizedBox(height: 16),
                Text(
                  'Required Skills',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: project.skills.map((skill) => Chip(
                    label: Text(skill),
                    backgroundColor: Colors.blue.withValues(alpha: 0.1),
                  )).toList(),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem('Budget', '${project.budget.min} - ${project.budget.max} ${project.budget.currency}'),
                    ),
                    Expanded(
                      child: _buildDetailItem('Duration', project.duration),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem('Location', project.location),
                    ),
                    Expanded(
                      child: _buildDetailItem('Experience', project.experienceLevel.toUpperCase()),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _navigateToApplication(project);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('Apply to This Project'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}

// Project Filters Model
class ProjectFilters {
  String? location;
  double? minBudget;
  double? maxBudget;
  String? experienceLevel;
  List<String>? skills;

  ProjectFilters({
    this.location,
    this.minBudget,
    this.maxBudget,
    this.experienceLevel,
    this.skills,
  });

  bool get hasActiveFilters {
    return (location != null && location!.isNotEmpty) ||
           minBudget != null ||
           maxBudget != null ||
           (experienceLevel != null && experienceLevel!.isNotEmpty) ||
           (skills != null && skills!.isNotEmpty);
  }

  ProjectFilters copyWith({
    String? location,
    double? minBudget,
    double? maxBudget,
    String? experienceLevel,
    List<String>? skills,
  }) {
    return ProjectFilters(
      location: location ?? this.location,
      minBudget: minBudget ?? this.minBudget,
      maxBudget: maxBudget ?? this.maxBudget,
      experienceLevel: experienceLevel ?? this.experienceLevel,
      skills: skills ?? this.skills,
    );
  }
}
