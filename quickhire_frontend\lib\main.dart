import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:quickhire/screens/chat_screen.dart';
import '../screens/joblisting_screen.dart';
import 'controllers/bottom_nav_bar_controller.dart';
import 'screens/main_wrapper.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'screens/decision_screen.dart';
import 'screens/emp_signup.dart';
import 'screens/forgot_password.dart';
import 'screens/js_dashboard.dart';
import 'screens/js_signup.dart';
import 'screens/lets_start.dart';
import 'screens/login.dart';
import 'screens/verify_otp.dart';
import 'screens/emp_dashboard.dart';
import 'screens/Meeting_Scheduling_Screen.dart';
import 'screens/create_jobListing_screen.dart';
import 'screens/enhanced_project_creation_screen.dart';
import 'screens/application_management_screen.dart';
import 'screens/what_do_you_want_to_do_screen.dart';
import 'screens/name_your_project_screen.dart';
import 'screens/jobseeker_swipe.dart';
import 'screens/chat_list_screen.dart';
import 'controllers/chat_controller.dart';
import 'services/service_chat.dart';
// Job Seeker Screens
import 'screens/jobseeker_signup_screen.dart';
import 'screens/jobseeker_profile_screen.dart';
import 'screens/jobseeker_dashboard_screen.dart';
import 'screens/project_discovery_screen.dart';
import 'screens/jobseeker_applications_screen.dart';


Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Awesome Notifications
  AwesomeNotifications().initialize(
    null, // Replace null with an app icon if needed for notification
    [
      NotificationChannel(
        channelKey: 'basic_channel',
        channelName: 'Basic Notifications',
        channelDescription: 'Notification channel for basic tests',
        defaultColor: const Color(0xFF9D50DD),
        ledColor: Colors.white,
      ),
    ],
  );

  // Request notification permissions
  AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
    if (!isAllowed) {
      // Request permissions
      AwesomeNotifications().requestPermissionToSendNotifications();
    }
  });

  // Initialize controllers
  Get.put(BottomNavController());

  // Initialize ChatService immediately
  final chatService = ChatService();
  Get.put(chatService);
  await chatService.init();
  
  // Initialize ChatController
  Get.put(ChatController());

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'QuickHire',
      theme: ThemeData(
        primarySwatch: Colors.amber,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      initialRoute: Login.id,
      routes: {
        Login.id: (context) => const Login(),
        LetsStart.id: (context) => const LetsStart(),
        DecisionScreen.id: (context) => const DecisionScreen(),
        JsSignup.id: (context) => const JsSignup(),
        JsDashboard.id: (context) => const JsDashboard(),
        EmpSignup.id: (context) => const EmpSignup(),
        EmpDashboard.id: (context) => const EmpDashboard(),
        ForgotPassword.id: (context) => const ForgotPassword(),
        VerifyOtp.id: (context) => const VerifyOtp(),
        Meeting_Scheduling_Screen.id: (context) => const Meeting_Scheduling_Screen(),
        WhatDoYouWantToDoScreen.id: (context) => const WhatDoYouWantToDoScreen(),
        NameYourProjectScreen.id: (context) => const NameYourProjectScreen(),
        CreateJobListingScreen.id: (context) => const CreateJobListingScreen(),
        EnhancedProjectCreationScreen.id: (context) => const EnhancedProjectCreationScreen(),
        JobSeekerSwipeScreen.id: (context) => const JobSeekerSwipeScreen(),
        ChatListScreen.id: (context) => ChatListScreen(),
        JobListingScreen.id: (context) => const JobListingScreen(),
        ChatScreen.id: (context) {
          // Extract arguments passed to ChatScreen
          final args = ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
          return ChatScreen(
              roomId: args['roomId'],
              roomName: args['roomName']
          );
        },
        '/main': (context) => const MainWrapper(),

        // Job Seeker Routes
        JobSeekerSignUpScreen.routeName: (context) => const JobSeekerSignUpScreen(),
        JobSeekerProfileScreen.routeName: (context) => const JobSeekerProfileScreen(),
        JobSeekerDashboardScreen.routeName: (context) => const JobSeekerDashboardScreen(),
        ProjectDiscoveryScreen.routeName: (context) => const ProjectDiscoveryScreen(),
        JobSeekerApplicationsScreen.routeName: (context) => const JobSeekerApplicationsScreen(),
      },
    );
  }
}
