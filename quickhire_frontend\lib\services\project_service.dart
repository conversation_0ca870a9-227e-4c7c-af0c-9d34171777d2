// lib/services/project_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import '../models/job_listing.dart';

class ProjectService {
  static const String baseUrl = AppConfig.projectsEndpoint;

  // Get headers with auth token
  Future<Map<String, String>> get _headers async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token') ?? '';
    
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Create a new project
  Future<JobListing> createProject(JobListing project) async {
    try {
      final headers = await _headers;
      final projectData = {
        'title': project.title,
        'description': project.description,
        'skills': project.skills,
        'location': project.location,
        'workType': project.workType,
        'budget': {
          'min': project.budget.min,
          'max': project.budget.max,
          'currency': project.budget.currency,
        },
        'duration': project.duration,
        'deadline': project.deadline?.toIso8601String(),
        'experienceLevel': project.experienceLevel,
        'projectType': project.projectType,
      };

      final response = await http.post(
        Uri.parse(baseUrl),
        headers: headers,
        body: json.encode(projectData),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return JobListing.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to create project');
      }
    } catch (e) {
      throw Exception('Error creating project: $e');
    }
  }

  // Get projects with filtering
  Future<List<JobListing>> getProjects({
    String? status,
    List<String>? skills,
    String? experienceLevel,
    String? projectType,
    String? location,
    double? minBudget,
    double? maxBudget,
    int page = 1,
    int limit = 10,
    String sortBy = 'createdAt',
    String sortOrder = 'desc',
  }) async {
    try {
      final headers = await _headers;
      
      // Build query parameters
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
        'sortBy': sortBy,
        'sortOrder': sortOrder,
      };

      if (status != null) queryParams['status'] = status;
      if (skills != null && skills.isNotEmpty) queryParams['skills'] = skills.join(',');
      if (experienceLevel != null) queryParams['experienceLevel'] = experienceLevel;
      if (projectType != null) queryParams['projectType'] = projectType;
      if (location != null) queryParams['location'] = location;
      if (minBudget != null) queryParams['minBudget'] = minBudget.toString();
      if (maxBudget != null) queryParams['maxBudget'] = maxBudget.toString();

      final uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final projectsJson = data['data'] as List;
        return projectsJson.map((json) => JobListing.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load projects: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching projects: $e');
    }
  }

  // Get employer's projects
  Future<List<JobListing>> getEmployerProjects() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/employer/list'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final projectsJson = data['data'] as List;
        return projectsJson.map((json) => JobListing.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load employer projects: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching employer projects: $e');
    }
  }

  // Get project by ID
  Future<JobListing> getProject(String projectId) async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/$projectId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return JobListing.fromJson(data['data']);
      } else {
        throw Exception('Failed to load project: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching project: $e');
    }
  }

  // Update project
  Future<JobListing> updateProject(String projectId, JobListing project) async {
    try {
      final headers = await _headers;
      final projectData = {
        'title': project.title,
        'description': project.description,
        'skills': project.skills,
        'location': project.location,
        'workType': project.workType,
        'budget': {
          'min': project.budget.min,
          'max': project.budget.max,
          'currency': project.budget.currency,
        },
        'duration': project.duration,
        'deadline': project.deadline?.toIso8601String(),
        'experienceLevel': project.experienceLevel,
        'projectType': project.projectType,
      };

      final response = await http.put(
        Uri.parse('$baseUrl/$projectId'),
        headers: headers,
        body: json.encode(projectData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return JobListing.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to update project');
      }
    } catch (e) {
      throw Exception('Error updating project: $e');
    }
  }

  // Delete project
  Future<bool> deleteProject(String projectId) async {
    try {
      final headers = await _headers;
      final response = await http.delete(
        Uri.parse('$baseUrl/$projectId'),
        headers: headers,
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error deleting project: $e');
    }
  }

  // Apply to project
  Future<bool> applyToProject(String projectId, {
    String? coverLetter,
    double? proposedBudget,
    String? estimatedDuration,
    List<Map<String, String>>? portfolio,
  }) async {
    try {
      final headers = await _headers;
      final applicationData = <String, dynamic>{};
      
      if (coverLetter != null) applicationData['coverLetter'] = coverLetter;
      if (proposedBudget != null) applicationData['proposedBudget'] = proposedBudget;
      if (estimatedDuration != null) applicationData['estimatedDuration'] = estimatedDuration;
      if (portfolio != null) applicationData['portfolio'] = portfolio;

      final response = await http.post(
        Uri.parse('$baseUrl/$projectId/apply'),
        headers: headers,
        body: json.encode(applicationData),
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error applying to project: $e');
    }
  }

  // Get project applications (for employers)
  Future<List<Application>> getProjectApplications(String projectId) async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/$projectId/applications'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final applicationsJson = data['data'] as List;
        return applicationsJson.map((json) => Application.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load applications: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching applications: $e');
    }
  }

  // Update application status (accept/reject)
  Future<bool> updateApplicationStatus(String projectId, String applicationId, String status) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/$projectId/applications/$applicationId'),
        headers: headers,
        body: json.encode({'status': status}),
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error updating application status: $e');
    }
  }

  // Complete project
  Future<bool> completeProject(String projectId, {double? actualBudget}) async {
    try {
      final headers = await _headers;
      final data = <String, dynamic>{};
      if (actualBudget != null) data['actualBudget'] = actualBudget;

      final response = await http.put(
        Uri.parse('$baseUrl/$projectId/complete'),
        headers: headers,
        body: json.encode(data),
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error completing project: $e');
    }
  }
}
