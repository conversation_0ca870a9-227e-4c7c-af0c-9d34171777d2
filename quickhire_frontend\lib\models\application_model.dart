// lib/models/application_model.dart

class PortfolioItem {
  final String title;
  final String description;
  final String url;

  PortfolioItem({
    required this.title,
    required this.description,
    required this.url,
  });

  factory PortfolioItem.fromJson(Map<String, dynamic> json) {
    return PortfolioItem(
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      url: json['url'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'url': url,
    };
  }
}

class JobSeekerInfo {
  final String id;
  final String name;
  final String email;
  final String location;
  final String? profilePicture;
  final String? bio;
  final List<String> skills;
  final String experienceLevel;
  final double? rating;
  final int completedProjects;

  JobSeekerInfo({
    required this.id,
    required this.name,
    required this.email,
    required this.location,
    this.profilePicture,
    this.bio,
    this.skills = const [],
    this.experienceLevel = 'entry',
    this.rating,
    this.completedProjects = 0,
  });

  factory JobSeekerInfo.fromJson(Map<String, dynamic> json) {
    return JobSeekerInfo(
      id: json['id'] ?? json['_id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      location: json['location'] ?? '',
      profilePicture: json['profilePicture'],
      bio: json['bio'],
      skills: json['skills'] != null ? List<String>.from(json['skills']) : [],
      experienceLevel: json['experienceLevel'] ?? 'entry',
      rating: json['rating']?.toDouble(),
      completedProjects: json['completedProjects'] ?? 0,
    );
  }
}

class DetailedApplication {
  final String id;
  final JobSeekerInfo jobSeeker;
  final String status;
  final DateTime appliedAt;
  final String? coverLetter;
  final double? proposedBudget;
  final String? estimatedDuration;
  final List<PortfolioItem> portfolio;
  final DateTime? reviewedAt;
  final String? reviewedBy;

  DetailedApplication({
    required this.id,
    required this.jobSeeker,
    required this.status,
    required this.appliedAt,
    this.coverLetter,
    this.proposedBudget,
    this.estimatedDuration,
    this.portfolio = const [],
    this.reviewedAt,
    this.reviewedBy,
  });

  factory DetailedApplication.fromJson(Map<String, dynamic> json) {
    return DetailedApplication(
      id: json['id'] ?? json['_id'] ?? '',
      jobSeeker: JobSeekerInfo.fromJson(json['jobSeeker'] ?? {}),
      status: json['status'] ?? 'pending',
      appliedAt: json['appliedAt'] != null 
          ? DateTime.parse(json['appliedAt']) 
          : DateTime.now(),
      coverLetter: json['coverLetter'],
      proposedBudget: json['proposedBudget']?.toDouble(),
      estimatedDuration: json['estimatedDuration'],
      portfolio: json['portfolio'] != null 
          ? (json['portfolio'] as List).map((item) => PortfolioItem.fromJson(item)).toList()
          : [],
      reviewedAt: json['reviewedAt'] != null 
          ? DateTime.parse(json['reviewedAt']) 
          : null,
      reviewedBy: json['reviewedBy'],
    );
  }

  bool get isPending => status == 'pending';
  bool get isAccepted => status == 'accepted';
  bool get isRejected => status == 'rejected';
  bool get isWithdrawn => status == 'withdrawn';

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'Pending Review';
      case 'accepted':
        return 'Accepted';
      case 'rejected':
        return 'Rejected';
      case 'withdrawn':
        return 'Withdrawn';
      default:
        return status.toUpperCase();
    }
  }
}

class ApplicationSubmission {
  final String projectId;
  final String coverLetter;
  final double? proposedBudget;
  final String? estimatedDuration;
  final List<PortfolioItem> portfolio;

  ApplicationSubmission({
    required this.projectId,
    required this.coverLetter,
    this.proposedBudget,
    this.estimatedDuration,
    this.portfolio = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'coverLetter': coverLetter,
      'proposedBudget': proposedBudget,
      'estimatedDuration': estimatedDuration,
      'portfolio': portfolio.map((item) => item.toJson()).toList(),
    };
  }
}

class ApplicationStats {
  final int totalApplications;
  final int pendingApplications;
  final int acceptedApplications;
  final int rejectedApplications;
  final double averageProposedBudget;
  final String mostCommonExperienceLevel;

  ApplicationStats({
    required this.totalApplications,
    required this.pendingApplications,
    required this.acceptedApplications,
    required this.rejectedApplications,
    required this.averageProposedBudget,
    required this.mostCommonExperienceLevel,
  });

  factory ApplicationStats.fromJson(Map<String, dynamic> json) {
    return ApplicationStats(
      totalApplications: json['totalApplications'] ?? 0,
      pendingApplications: json['pendingApplications'] ?? 0,
      acceptedApplications: json['acceptedApplications'] ?? 0,
      rejectedApplications: json['rejectedApplications'] ?? 0,
      averageProposedBudget: (json['averageProposedBudget'] ?? 0).toDouble(),
      mostCommonExperienceLevel: json['mostCommonExperienceLevel'] ?? 'entry',
    );
  }

  double get acceptanceRate {
    if (totalApplications == 0) return 0.0;
    return (acceptedApplications / totalApplications) * 100;
  }

  double get rejectionRate {
    if (totalApplications == 0) return 0.0;
    return (rejectedApplications / totalApplications) * 100;
  }
}
