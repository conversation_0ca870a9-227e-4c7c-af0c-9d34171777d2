// controllers/jobSeekerController.js
const JobSeekerProfile = require('../models/JobSeeker');
const User = require('../models/User');

// @desc    Get job seeker profile
// @route   GET /api/v1/jobseekers/profile
// @access  Private (Job seekers only)
exports.getProfile = async (req, res) => {
  try {
    const profile = await JobSeekerProfile.findOne({ user: req.user.id })
      .populate('user', 'name email');

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    res.status(200).json({
      success: true,
      data: profile
    });
  } catch (error) {
    console.error('Get Profile Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not fetch profile',
      error: error.message
    });
  }
};

// @desc    Update job seeker profile
// @route   PUT /api/v1/jobseekers/profile
// @access  Private (Job seekers only)
exports.updateProfile = async (req, res) => {
  try {
    const {
      title,
      bio,
      skills,
      experience,
      education,
      location,
      hourlyRate,
      availability,
      portfolio,
      languages,
      certifications,
      preferences
    } = req.body;

    let profile = await JobSeekerProfile.findOne({ user: req.user.id });

    if (!profile) {
      // Create new profile if it doesn't exist
      profile = await JobSeekerProfile.create({
        user: req.user.id,
        title,
        bio,
        skills,
        experience,
        education,
        location,
        hourlyRate,
        availability,
        portfolio,
        languages,
        certifications,
        preferences
      });
    } else {
      // Update existing profile
      profile = await JobSeekerProfile.findOneAndUpdate(
        { user: req.user.id },
        {
          title,
          bio,
          skills,
          experience,
          education,
          location,
          hourlyRate,
          availability,
          portfolio,
          languages,
          certifications,
          preferences,
          updatedAt: Date.now()
        },
        { new: true, runValidators: true }
      );
    }

    await profile.populate('user', 'name email');

    res.status(200).json({
      success: true,
      data: profile,
      message: 'Profile updated successfully'
    });
  } catch (error) {
    console.error('Update Profile Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not update profile',
      error: error.message
    });
  }
};

// @desc    Upload profile picture
// @route   POST /api/v1/jobseekers/profile/picture
// @access  Private (Job seekers only)
exports.uploadProfilePicture = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Please upload a file'
      });
    }

    const profile = await JobSeekerProfile.findOneAndUpdate(
      { user: req.user.id },
      { profilePicture: req.file.path },
      { new: true, runValidators: true }
    );

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    res.status(200).json({
      success: true,
      data: profile,
      message: 'Profile picture uploaded successfully'
    });
  } catch (error) {
    console.error('Upload Profile Picture Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not upload profile picture',
      error: error.message
    });
  }
};

// @desc    Delete profile picture
// @route   DELETE /api/v1/jobseekers/profile/picture
// @access  Private (Job seekers only)
exports.deleteProfilePicture = async (req, res) => {
  try {
    const profile = await JobSeekerProfile.findOneAndUpdate(
      { user: req.user.id },
      { $unset: { profilePicture: 1 } },
      { new: true }
    );

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    res.status(200).json({
      success: true,
      data: profile,
      message: 'Profile picture deleted successfully'
    });
  } catch (error) {
    console.error('Delete Profile Picture Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not delete profile picture',
      error: error.message
    });
  }
};

// @desc    Get dashboard stats for job seeker
// @route   GET /api/v1/jobseekers/dashboard
// @access  Private (Job seekers only)
exports.getDashboardStats = async (req, res) => {
  try {
    const Project = require('../models/Project');
    
    // Get applications count
    const applicationsCount = await Project.countDocuments({
      'applications.jobSeeker': req.user.id
    });

    // Get accepted applications count
    const acceptedApplicationsCount = await Project.countDocuments({
      'applications.jobSeeker': req.user.id,
      'applications.status': 'accepted'
    });

    // Get pending applications count
    const pendingApplicationsCount = await Project.countDocuments({
      'applications.jobSeeker': req.user.id,
      'applications.status': 'pending'
    });

    // Get profile completion percentage
    const profile = await JobSeekerProfile.findOne({ user: req.user.id });
    let profileCompletion = 0;
    if (profile) {
      const fields = ['title', 'bio', 'skills', 'experience', 'education', 'location'];
      const completedFields = fields.filter(field => profile[field] && profile[field].length > 0);
      profileCompletion = Math.round((completedFields.length / fields.length) * 100);
    }

    res.status(200).json({
      success: true,
      data: {
        applicationsCount,
        acceptedApplicationsCount,
        pendingApplicationsCount,
        profileCompletion,
        totalProjects: applicationsCount,
        activeProjects: acceptedApplicationsCount
      }
    });
  } catch (error) {
    console.error('Get Dashboard Stats Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not fetch dashboard stats',
      error: error.message
    });
  }
};
