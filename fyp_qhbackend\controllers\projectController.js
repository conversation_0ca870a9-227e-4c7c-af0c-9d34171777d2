// controllers/projectController.js
const Project = require('../models/Project');
const JobSeekerProfile = require('../models/JobSeeker');
const User = require('../models/User');

// @desc    Create a new project
// @route   POST /api/v1/projects
// @access  Private (Employers only)
exports.createProject = async (req, res) => {
  try {
    // Check if user is an employer
    if (req.user.role !== 'employer') {
      return res.status(403).json({
        success: false,
        message: 'Only employers can create projects'
      });
    }

    // Add employer ID to project data
    req.body.employer = req.user.id;

    // Convert skills string to array if it's a string
    if (req.body.skills && typeof req.body.skills === 'string') {
      req.body.skills = req.body.skills.split(',').map(skill => skill.trim());
    }

    // Validate budget structure
    if (req.body.budget) {
      if (typeof req.body.budget === 'number') {
        // Convert single budget to min/max structure
        req.body.budget = {
          min: req.body.budget,
          max: req.body.budget,
          currency: req.body.currency || 'USD'
        };
      } else if (req.body.budget.min && req.body.budget.max) {
        // Ensure min is not greater than max
        if (req.body.budget.min > req.body.budget.max) {
          return res.status(400).json({
            success: false,
            message: 'Minimum budget cannot be greater than maximum budget'
          });
        }
      }
    }

    // Validate deadline
    if (req.body.deadline) {
      const deadline = new Date(req.body.deadline);
      if (deadline <= new Date()) {
        return res.status(400).json({
          success: false,
          message: 'Deadline must be in the future'
        });
      }
    }

    // Create project
    const project = await Project.create(req.body);

    // Populate employer details for response
    const populatedProject = await Project.findById(project._id).populate({
      path: 'employer',
      select: 'name email'
    });

    res.status(201).json({
      success: true,
      message: 'Project created successfully',
      data: populatedProject
    });
  } catch (error) {
    console.error('Create Project Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not create project',
      error: error.message
    });
  }
};

// @desc    Get all projects with filtering and pagination
// @route   GET /api/v1/projects
// @access  Public
exports.getProjects = async (req, res) => {
  try {
    // Build query object
    let query = {};

    // Filter by status
    if (req.query.status) {
      query.status = req.query.status;
    } else {
      // Default to open projects only for public access
      query.status = 'open';
    }

    // Filter by skills
    if (req.query.skills) {
      const skills = req.query.skills.split(',').map(skill => skill.trim());
      query.skills = { $in: skills };
    }

    // Filter by experience level
    if (req.query.experienceLevel) {
      query.experienceLevel = req.query.experienceLevel;
    }

    // Filter by project type
    if (req.query.projectType) {
      query.projectType = req.query.projectType;
    }

    // Filter by location
    if (req.query.location) {
      query.location = new RegExp(req.query.location, 'i');
    }

    // Filter by budget range
    if (req.query.minBudget || req.query.maxBudget) {
      query['budget.min'] = {};
      query['budget.max'] = {};

      if (req.query.minBudget) {
        query['budget.max'] = { $gte: parseInt(req.query.minBudget) };
      }
      if (req.query.maxBudget) {
        query['budget.min'] = { $lte: parseInt(req.query.maxBudget) };
      }
    }

    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Sort options
    let sortBy = {};
    if (req.query.sortBy) {
      const sortField = req.query.sortBy;
      const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;
      sortBy[sortField] = sortOrder;
    } else {
      sortBy = { createdAt: -1 }; // Default: newest first
    }

    // Execute query
    const projects = await Project.find(query)
      .populate({
        path: 'employer',
        select: 'name'
      })
      .sort(sortBy)
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const total = await Project.countDocuments(query);

    res.status(200).json({
      success: true,
      count: projects.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      data: projects
    });
  } catch (error) {
    console.error('Get Projects Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not fetch projects',
      error: error.message
    });
  }
};

// @desc    Get project by ID
// @route   GET /api/v1/projects/:id
// @access  Public
exports.getProject = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id).populate({
      path: 'employer',
      select: 'name'
    }).populate({
      path: 'acceptedBy.jobSeeker',
      select: 'name email'
    });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    res.status(200).json({
      success: true,
      data: project
    });
  } catch (error) {
    console.error('Get Project Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not fetch project',
      error: error.message
    });
  }
};

// @desc    Update project details
// @route   PUT /api/v1/projects/:id
// @access  Private (Project owner/employer only)
exports.updateProject = async (req, res) => {
    try {
      let project = await Project.findById(req.params.id);
      
      if (!project) {
        return res.status(404).json({
          success: false,
          message: 'Project not found'
        });
      }
      
      // Check if user is the project owner
      if (project.employer.toString() !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to update this project'
        });
      }
      
      // Convert skills string to array if it's a string
      if (req.body.skills && typeof req.body.skills === 'string') {
        req.body.skills = req.body.skills.split(',').map(skill => skill.trim());
      }
      
      // Don't allow changing certain fields if the project already has applicants
      if (project.acceptedBy && project.acceptedBy.length > 0) {
        const safeFields = ['title', 'description', 'budget', 'duration', 'status'];
        const updateFields = Object.keys(req.body);
        
        const unsafeUpdate = updateFields.some(field => !safeFields.includes(field));
        
        if (unsafeUpdate) {
          return res.status(400).json({
            success: false,
            message: 'Cannot update core project details (skills, location) after receiving applications'
          });
        }
      }
      
      // Update project
      project = await Project.findByIdAndUpdate(req.params.id, req.body, {
        new: true,
        runValidators: true
      });
      
      res.status(200).json({
        success: true,
        data: project,
        message: 'Project updated successfully'
      });
    } catch (error) {
      console.error('Update Project Error:', error);
      res.status(500).json({
        success: false,
        message: 'Could not update project',
        error: error.message
      });
    }
  };

// @desc    Get matched projects for job seeker
// @route   GET /api/v1/projects/matches
// @access  Private (Job seekers only)
exports.getMatchedProjects = async (req, res) => {
    try {
      // Check if user is a job seeker
      if (req.user.role !== 'jobseeker') {
        return res.status(403).json({
          success: false,
          message: 'Only job seekers can access project matches'
        });
      }
      
      // Get job seeker profile to find skills
      const jobSeekerProfile = await JobSeekerProfile.findOne({ user: req.user.id });
      
      if (!jobSeekerProfile) {
        return res.status(404).json({
          success: false,
          message: 'Job seeker profile not found'
        });
      }
      
      // Get skills from job seeker profile, but location from the user object
      const { skills } = jobSeekerProfile;
      const { location } = req.user; // Get location from user object instead
      
      // Query params for filtering
      const { locationPreference } = req.query;
      
      // Base query for open projects only
      let query = { status: 'open' };
      
      // Match by location if locationPreference is true
      if (locationPreference === 'true' && location) {
        // Use exact location match (case-insensitive)
        query.location = new RegExp(`^${location}$`, 'i');
      }
      
      // Find all open projects that match the query
      let projects = await Project.find(query)
        .populate({
          path: 'employer',
          select: 'name'
        });
      
      // Calculate skill match score for all projects
      let matchedProjects = projects.map(project => {
        // Calculate match score based on skills overlap
        let matchScore = 0;
        let matchedSkills = [];
        
        if (skills && skills.length > 0 && project.skills && project.skills.length > 0) {
          const userSkillsSet = new Set(skills.map(s => s.toLowerCase()));
          
          project.skills.forEach(skill => {
            const lowerSkill = skill.toLowerCase();
            if (userSkillsSet.has(lowerSkill)) {
              matchScore++;
              matchedSkills.push(skill);
            }
          });
          
          // Convert to percentage based on total required skills
          matchScore = (matchScore / project.skills.length) * 100;
        }
        
        // Check if job seeker has already accepted this project
        const alreadyAccepted = project.acceptedBy.some(
          item => item.jobSeeker.toString() === req.user.id
        );
        
        return {
          ...project.toObject(),
          matchScore: Math.round(matchScore),
          matchedSkills,
          alreadyAccepted
        };
      });
      
      // Filter out projects with zero skill matches
      matchedProjects = matchedProjects.filter(project => project.matchScore > 0);
      
      // Sort by match score (highest first)
      matchedProjects.sort((a, b) => b.matchScore - a.matchScore);
      
      res.status(200).json({
        success: true,
        count: matchedProjects.length,
        data: matchedProjects
      });
    } catch (error) {
      console.error('Get Matched Projects Error:', error);
      res.status(500).json({
        success: false,
        message: 'Could not fetch matched projects',
        error: error.message
      });
    }
  };

// @desc    Accept a project (for job seekers)
// @route   POST /api/v1/projects/:id/accept
// @access  Private (Job seekers only)
exports.acceptProject = async (req, res) => {
  try {
    // Check if user is a job seeker
    if (req.user.role !== 'jobseeker') {
      return res.status(403).json({
        success: false,
        message: 'Only job seekers can accept projects'
      });
    }
    
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }
    
    // Check if project is still open
    if (project.status !== 'open') {
      return res.status(400).json({
        success: false,
        message: 'This project is no longer accepting applications'
      });
    }
    
    // Check if job seeker has already accepted this project
    const alreadyAccepted = project.acceptedBy.some(
      item => item.jobSeeker.toString() === req.user.id
    );
    
    if (alreadyAccepted) {
      return res.status(400).json({
        success: false,
        message: 'You have already accepted this project'
      });
    }
    
    // Add job seeker to acceptedBy array
    project.acceptedBy.push({
      jobSeeker: req.user.id,
      status: 'pending',
      acceptedAt: Date.now()
    });
    
    await project.save();
    
    res.status(200).json({
      success: true,
      message: 'Project accepted successfully',
      data: project
    });
  } catch (error) {
    console.error('Accept Project Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not accept project',
      error: error.message
    });
  }
};

// @desc    Get job seekers who accepted a project (for employers)
// @route   GET /api/v1/projects/:id/applicants
// @access  Private (Project owner only)
exports.getProjectApplicants = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }
    
    // Check if the requesting user is the project owner
    if (project.employer.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to view applicants for this project'
      });
    }
    
    // Populate job seeker details
    const populatedProject = await Project.findById(req.params.id)
      .populate({
        path: 'acceptedBy.jobSeeker',
        select: 'name email'
      });
    
    // Get detailed profiles for each job seeker
    const applicants = await Promise.all(
      populatedProject.acceptedBy.map(async (applicant) => {
        const profile = await JobSeekerProfile.findOne({ 
          user: applicant.jobSeeker._id 
        });
        
        return {
          id: applicant.jobSeeker._id,
          name: applicant.jobSeeker.name,
          email: applicant.jobSeeker.email,
          status: applicant.status,
          acceptedAt: applicant.acceptedAt,
          profile: profile || {}
        };
      })
    );
    
    res.status(200).json({
      success: true,
      count: applicants.length,
      data: applicants
    });
  } catch (error) {
    console.error('Get Project Applicants Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not fetch project applicants',
      error: error.message
    });
  }
};

// @desc    Update applicant status (accept/reject)
// @route   PUT /api/v1/projects/:id/applicants/:applicantId
// @access  Private (Project owner only)
exports.updateApplicantStatus = async (req, res) => {
  try {
    const { status } = req.body;
    
    // Validate status
    if (!status || !['accepted', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid status (accepted or rejected)'
      });
    }
    
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }
    
    // Check if the requesting user is the project owner
    if (project.employer.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to update applicants for this project'
      });
    }
    
    // Find the applicant in the project
    const applicantIndex = project.acceptedBy.findIndex(
      item => item.jobSeeker.toString() === req.params.applicantId
    );
    
    if (applicantIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Applicant not found for this project'
      });
    }
    
    // Update the applicant status
    project.acceptedBy[applicantIndex].status = status;
    await project.save();
    
    res.status(200).json({
      success: true,
      message: `Applicant status updated to ${status}`,
      data: project.acceptedBy[applicantIndex]
    });
  } catch (error) {
    console.error('Update Applicant Status Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not update applicant status',
      error: error.message
    });
  }
};

// @desc    Get projects created by the employer
// @route   GET /api/v1/projects/employer
// @access  Private (Employers only)
exports.getEmployerProjects = async (req, res) => {
  try {
    // Check if user is an employer
    if (req.user.role !== 'employer') {
      return res.status(403).json({
        success: false,
        message: 'Only employers can access their projects'
      });
    }
    
    const projects = await Project.find({ employer: req.user.id });
    
    res.status(200).json({
      success: true,
      count: projects.length,
      data: projects
    });
  } catch (error) {
    console.error('Get Employer Projects Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not fetch employer projects',
      error: error.message
    });
  }
};

// @desc    Apply to a project
// @route   POST /api/v1/projects/:id/apply
// @access  Private (Job seekers only)
exports.applyToProject = async (req, res) => {
  try {
    // Check if user is a job seeker
    if (req.user.role !== 'jobseeker') {
      return res.status(403).json({
        success: false,
        message: 'Only job seekers can apply to projects'
      });
    }

    const { coverLetter, proposedBudget, estimatedDuration, portfolio } = req.body;

    const project = await Project.findById(req.params.id);

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check if project is still open
    if (project.status !== 'open') {
      return res.status(400).json({
        success: false,
        message: 'This project is no longer accepting applications'
      });
    }

    // Check if job seeker has already applied
    const alreadyApplied = project.applications.some(
      app => app.jobSeeker.toString() === req.user.id
    );

    if (alreadyApplied) {
      return res.status(400).json({
        success: false,
        message: 'You have already applied to this project'
      });
    }

    // Add application to project
    project.applications.push({
      jobSeeker: req.user.id,
      coverLetter,
      proposedBudget,
      estimatedDuration,
      portfolio: portfolio || []
    });

    await project.save();

    res.status(200).json({
      success: true,
      message: 'Application submitted successfully'
    });
  } catch (error) {
    console.error('Apply to Project Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not submit application',
      error: error.message
    });
  }
};

// @desc    Get applications for a project
// @route   GET /api/v1/projects/:id/applications
// @access  Private (Project owner only)
exports.getProjectApplications = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id)
      .populate({
        path: 'applications.jobSeeker',
        select: 'name email location'
      });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check if the requesting user is the project owner
    if (project.employer.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to view applications for this project'
      });
    }

    // Get detailed profiles for each applicant
    const applications = await Promise.all(
      project.applications.map(async (application) => {
        const profile = await JobSeekerProfile.findOne({
          user: application.jobSeeker._id
        });

        return {
          id: application._id,
          jobSeeker: {
            id: application.jobSeeker._id,
            name: application.jobSeeker.name,
            email: application.jobSeeker.email,
            location: application.jobSeeker.location,
            profile: profile || {}
          },
          status: application.status,
          appliedAt: application.appliedAt,
          coverLetter: application.coverLetter,
          proposedBudget: application.proposedBudget,
          estimatedDuration: application.estimatedDuration,
          portfolio: application.portfolio,
          reviewedAt: application.reviewedAt
        };
      })
    );

    res.status(200).json({
      success: true,
      count: applications.length,
      data: applications
    });
  } catch (error) {
    console.error('Get Project Applications Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not fetch project applications',
      error: error.message
    });
  }
};

// @desc    Update application status (accept/reject)
// @route   PUT /api/v1/projects/:id/applications/:applicationId
// @access  Private (Project owner only)
exports.updateApplicationStatus = async (req, res) => {
  try {
    const { status } = req.body;

    // Validate status
    if (!status || !['accepted', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid status (accepted or rejected)'
      });
    }

    const project = await Project.findById(req.params.id);

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check if the requesting user is the project owner
    if (project.employer.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to update applications for this project'
      });
    }

    // Find the application
    const applicationIndex = project.applications.findIndex(
      app => app._id.toString() === req.params.applicationId
    );

    if (applicationIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }

    // Update the application status
    project.applications[applicationIndex].status = status;
    project.applications[applicationIndex].reviewedAt = new Date();
    project.applications[applicationIndex].reviewedBy = req.user.id;

    // If accepted, set as selected job seeker and update project status
    if (status === 'accepted') {
      project.selectedJobSeeker = project.applications[applicationIndex].jobSeeker;
      project.selectedAt = new Date();
      project.status = 'in-progress';
    }

    await project.save();

    res.status(200).json({
      success: true,
      message: `Application ${status} successfully`,
      data: project.applications[applicationIndex]
    });
  } catch (error) {
    console.error('Update Application Status Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not update application status',
      error: error.message
    });
  }
};

// @desc    Complete a project
// @route   PUT /api/v1/projects/:id/complete
// @access  Private (Project owner only)
exports.completeProject = async (req, res) => {
  try {
    const { actualBudget } = req.body;

    const project = await Project.findById(req.params.id);

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check if the requesting user is the project owner
    if (project.employer.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to complete this project'
      });
    }

    // Check if project is in progress
    if (project.status !== 'in-progress') {
      return res.status(400).json({
        success: false,
        message: 'Only in-progress projects can be completed'
      });
    }

    // Update project status
    project.status = 'completed';
    project.completedAt = new Date();
    if (actualBudget) {
      project.actualBudget = actualBudget;
    }

    await project.save();

    res.status(200).json({
      success: true,
      message: 'Project completed successfully',
      data: project
    });
  } catch (error) {
    console.error('Complete Project Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not complete project',
      error: error.message
    });
  }
};

// @desc    Delete a project
// @route   DELETE /api/v1/projects/:id
// @access  Private (Project owner only)
exports.deleteProject = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check if the requesting user is the project owner
    if (project.employer.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to delete this project'
      });
    }

    // Check if project has applications
    if (project.applications && project.applications.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete project with existing applications. Cancel the project instead.'
      });
    }

    await Project.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Project deleted successfully'
    });
  } catch (error) {
    console.error('Delete Project Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not delete project',
      error: error.message
    });
  }
};

// @desc    Get projects accepted by the job seeker
// @route   GET /api/v1/projects/jobseeker
// @access  Private (Job seekers only)
exports.getAcceptedProjects = async (req, res) => {
  try {
    // Check if user is a job seeker
    if (req.user.role !== 'jobseeker') {
      return res.status(403).json({
        success: false,
        message: 'Only job seekers can access their accepted projects'
      });
    }
    
    // Find projects where this job seeker is in the acceptedBy array
    const projects = await Project.find({
      'acceptedBy.jobSeeker': req.user.id
    }).populate({
      path: 'employer',
      select: 'name'
    });
    
    res.status(200).json({
      success: true,
      count: projects.length,
      data: projects
    });
  } catch (error) {
    console.error('Get Accepted Projects Error:', error);
    res.status(500).json({
      success: false,
      message: 'Could not fetch accepted projects',
      error: error.message
    });
  }
};