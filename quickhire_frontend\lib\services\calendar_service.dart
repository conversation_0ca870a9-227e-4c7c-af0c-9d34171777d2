// lib/services/calendar_service.dart
import 'dart:io';
import 'package:url_launcher/url_launcher.dart';
import '../models/message_model.dart';

class CalendarService {

  // Generate calendar event URL for external calendar apps
  String generateCalendarEventUrl(Meeting meeting) {
    final startTime = meeting.scheduledAt.toUtc();
    final endTime = meeting.endTime.toUtc();

    // Format dates for calendar URL (YYYYMMDDTHHMMSSZ)
    final startFormatted = _formatDateForCalendar(startTime);
    final endFormatted = _formatDateForCalendar(endTime);

    // Create Google Calendar URL
    final title = Uri.encodeComponent(meeting.title);
    final description = Uri.encodeComponent(_buildEventDescription(meeting));
    final location = Uri.encodeComponent(meeting.meetingUrl ?? 'Video Call');

    return 'https://calendar.google.com/calendar/render?action=TEMPLATE'
        '&text=$title'
        '&dates=$startFormatted/$endFormatted'
        '&details=$description'
        '&location=$location';
  }

  // Open calendar app to add event
  Future<bool> addToCalendar(Meeting meeting) async {
    try {
      final calendarUrl = generateCalendarEventUrl(meeting);
      final uri = Uri.parse(calendarUrl);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      } else {
        throw Exception('Cannot open calendar app');
      }
    } catch (e) {
      throw Exception('Error adding to calendar: $e');
    }
  }

  // Format date for calendar URL (YYYYMMDDTHHMMSSZ)
  String _formatDateForCalendar(DateTime dateTime) {
    return '${dateTime.year.toString().padLeft(4, '0')}'
        '${dateTime.month.toString().padLeft(2, '0')}'
        '${dateTime.day.toString().padLeft(2, '0')}'
        'T'
        '${dateTime.hour.toString().padLeft(2, '0')}'
        '${dateTime.minute.toString().padLeft(2, '0')}'
        '${dateTime.second.toString().padLeft(2, '0')}'
        'Z';
  }

  // Format date for ICS file (YYYYMMDDTHHMMSSZ)
  String _formatDateForICS(DateTime dateTime) {
    return _formatDateForCalendar(dateTime);
  }

  // Generate ICS file content for calendar import
  String generateICSContent(Meeting meeting) {
    final startTime = meeting.scheduledAt.toUtc();
    final endTime = meeting.endTime.toUtc();

    final startFormatted = _formatDateForICS(startTime);
    final endFormatted = _formatDateForICS(endTime);
    final now = _formatDateForICS(DateTime.now().toUtc());

    return '''BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//QuickHire//Meeting//EN
BEGIN:VEVENT
UID:quickhire-meeting-${meeting.id}@quickhire.com
DTSTAMP:$now
DTSTART:$startFormatted
DTEND:$endFormatted
SUMMARY:${meeting.title}
DESCRIPTION:${_buildEventDescription(meeting)}
LOCATION:${meeting.meetingUrl ?? 'Video Call'}
STATUS:CONFIRMED
BEGIN:VALARM
TRIGGER:-PT15M
ACTION:DISPLAY
DESCRIPTION:Meeting reminder
END:VALARM
BEGIN:VALARM
TRIGGER:-PT5M
ACTION:DISPLAY
DESCRIPTION:Meeting starting soon
END:VALARM
END:VEVENT
END:VCALENDAR''';
  }

  // Check if user might be available (simplified version)
  bool isLikelyAvailable(DateTime startTime, DateTime endTime) {
    final now = DateTime.now();

    // Check if it's in the past
    if (startTime.isBefore(now)) return false;

    // Check if it's during reasonable working hours (9 AM - 6 PM)
    final hour = startTime.hour;
    if (hour < 9 || hour > 18) return false;

    // Check if it's on weekend
    final weekday = startTime.weekday;
    if (weekday == DateTime.saturday || weekday == DateTime.sunday) return false;

    return true;
  }

  // Generate suggested time slots
  List<DateTime> generateSuggestedTimeSlots({
    required DateTime startDate,
    required DateTime endDate,
    required int durationMinutes,
    int workingHoursStart = 9,
    int workingHoursEnd = 17,
  }) {
    final slots = <DateTime>[];
    DateTime currentDate = startDate;

    while (currentDate.isBefore(endDate) && slots.length < 20) {
      // Skip weekends
      if (currentDate.weekday == DateTime.saturday ||
          currentDate.weekday == DateTime.sunday) {
        currentDate = currentDate.add(const Duration(days: 1));
        continue;
      }

      // Generate slots for working hours
      for (int hour = workingHoursStart; hour < workingHoursEnd; hour += 2) {
        final slotTime = DateTime(
          currentDate.year,
          currentDate.month,
          currentDate.day,
          hour,
          0,
        );

        // Skip past times
        if (slotTime.isAfter(DateTime.now())) {
          slots.add(slotTime);
        }
      }

      currentDate = currentDate.add(const Duration(days: 1));
    }

    return slots;
  }

  // Simplified calendar integration - opens external calendar app
  Future<bool> addMeetingToCalendar(Meeting meeting) async {
    try {
      return await addToCalendar(meeting);
    } catch (e) {
      throw Exception('Error adding meeting to calendar: $e');
    }
  }



  // Build event description with meeting details
  String _buildEventDescription(Meeting meeting) {
    final buffer = StringBuffer();
    
    buffer.writeln('QuickHire Meeting');
    buffer.writeln('');
    buffer.writeln('Description: ${meeting.description}');
    buffer.writeln('Duration: ${meeting.durationMinutes} minutes');
    
    if (meeting.meetingUrl != null) {
      buffer.writeln('');
      buffer.writeln('Join Video Call: ${meeting.meetingUrl}');
    }
    
    if (meeting.meetingId != null) {
      buffer.writeln('Meeting ID: ${meeting.meetingId}');
    }
    
    if (meeting.password != null) {
      buffer.writeln('Password: ${meeting.password}');
    }
    
    buffer.writeln('');
    buffer.writeln('Participants: ${meeting.participants.length}');
    buffer.writeln('Organized via QuickHire');
    
    return buffer.toString();
  }

  // Create calendar reminder for upcoming meetings
  Future<void> scheduleNotificationReminder(Meeting meeting) async {
    try {
      // This would integrate with local notifications
      // For now, we'll just add calendar reminders
      await addMeetingToCalendar(meeting);
    } catch (e) {
      // Handle error silently for now
    }
  }
}
