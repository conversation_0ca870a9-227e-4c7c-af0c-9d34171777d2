// lib/models/job_listing.dart

class Budget {
  final double min;
  final double max;
  final String currency;

  Budget({
    required this.min,
    required this.max,
    this.currency = 'USD',
  });

  factory Budget.fromJson(Map<String, dynamic> json) {
    return Budget(
      min: (json['min'] ?? 0).toDouble(),
      max: (json['max'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'USD',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'min': min,
      'max': max,
      'currency': currency,
    };
  }
}

class Application {
  final String id;
  final String jobSeekerId;
  final String status;
  final DateTime appliedAt;
  final String? coverLetter;
  final double? proposedBudget;
  final String? estimatedDuration;

  Application({
    required this.id,
    required this.jobSeekerId,
    required this.status,
    required this.appliedAt,
    this.coverLetter,
    this.proposedBudget,
    this.estimatedDuration,
  });

  factory Application.fromJson(Map<String, dynamic> json) {
    return Application(
      id: json['_id'] ?? json['id'] ?? '',
      jobSeekerId: json['jobSeeker'] ?? '',
      status: json['status'] ?? 'pending',
      appliedAt: json['appliedAt'] != null
          ? DateTime.parse(json['appliedAt'])
          : DateTime.now(),
      coverLetter: json['coverLetter'],
      proposedBudget: json['proposedBudget']?.toDouble(),
      estimatedDuration: json['estimatedDuration'],
    );
  }
}

class JobListing {
  final String id;
  final String employer;
  final String title;
  final String description;
  final List<String> skills;
  final String location;
  final String workType;
  final Budget budget;
  final String duration;
  final DateTime? deadline;
  final String experienceLevel;
  final String projectType;
  final String status;
  final List<Application> applications;
  final String? selectedJobSeeker;
  final DateTime? selectedAt;
  final bool isPublic;
  final bool isFeatured;
  final int viewsCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  JobListing({
    required this.id,
    required this.employer,
    required this.title,
    required this.description,
    required this.skills,
    required this.location,
    required this.workType,
    required this.budget,
    required this.duration,
    this.deadline,
    required this.experienceLevel,
    required this.projectType,
    required this.status,
    required this.applications,
    this.selectedJobSeeker,
    this.selectedAt,
    this.isPublic = true,
    this.isFeatured = false,
    this.viewsCount = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  factory JobListing.fromJson(Map<String, dynamic> json) {
    return JobListing(
      id: json['_id'] ?? json['id'] ?? '',
      employer: json['employer'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? json['jobDescription'] ?? '',
      skills: json['skills'] != null
          ? List<String>.from(json['skills'])
          : [],
      location: json['location'] ?? '',
      workType: json['workType'] ?? json['employmentType'] ?? 'remote',
      budget: json['budget'] != null
          ? Budget.fromJson(json['budget'])
          : Budget(min: 0, max: 0),
      duration: json['duration'] ?? '',
      deadline: json['deadline'] != null
          ? DateTime.parse(json['deadline'])
          : null,
      experienceLevel: json['experienceLevel'] ?? 'entry',
      projectType: json['projectType'] ?? 'fixed-price',
      status: json['status'] ?? 'open',
      applications: json['applications'] != null
          ? (json['applications'] as List).map((app) => Application.fromJson(app)).toList()
          : [],
      selectedJobSeeker: json['selectedJobSeeker'],
      selectedAt: json['selectedAt'] != null
          ? DateTime.parse(json['selectedAt'])
          : null,
      isPublic: json['isPublic'] ?? true,
      isFeatured: json['isFeatured'] ?? false,
      viewsCount: json['viewsCount'] ?? 0,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
    );
  }

  // Convert JobListing to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employer': employer,
      'title': title,
      'description': description,
      'skills': skills,
      'location': location,
      'workType': workType,
      'budget': budget.toJson(),
      'duration': duration,
      'deadline': deadline?.toIso8601String(),
      'experienceLevel': experienceLevel,
      'projectType': projectType,
      'status': status,
      'applications': applications.map((app) => {
        'jobSeeker': app.jobSeekerId,
        'status': app.status,
        'appliedAt': app.appliedAt.toIso8601String(),
        'coverLetter': app.coverLetter,
        'proposedBudget': app.proposedBudget,
        'estimatedDuration': app.estimatedDuration,
      }).toList(),
      'selectedJobSeeker': selectedJobSeeker,
      'selectedAt': selectedAt?.toIso8601String(),
      'isPublic': isPublic,
      'isFeatured': isFeatured,
      'viewsCount': viewsCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Helper getters for backward compatibility
  String get projectName => title;
  String get jobDescription => description;
  String get employmentType => workType;
  bool get isShortlisted => false; // Default value for compatibility
  DateTime get startDate => deadline ?? createdAt;
}


/*
class JobListing {
  // Typically managed by the backend via authentication
  final String employer;

  final String projectName;
  final String jobDescription;
  final String industry;
  final String employmentType;
  final String title;
  final String experienceLevel;
  final String? backgroundPreferences;
  final DateTime startDate;
  final String location;
  final String? candidateLocation;
  final double? radius;
  final String relocationOptions;

  JobListing({
    required this.employer,
    required this.projectName,
    required this.jobDescription,
    required this.industry,
    required this.employmentType,
    required this.title,
    required this.experienceLevel,
    this.backgroundPreferences,
    required this.startDate,
    required this.location,
    this.candidateLocation,
    this.radius,
    required this.relocationOptions,
  }

factory JobListing.fromJson(Map<String, dynamic> json) {
return JobListing(
id: json['id'] ?? '',
employer: json['employer'] ?? '',
projectName: json['projectName'] ?? '',
jobDescription: json['jobDescription'] ?? '',
industry: json['industry'] ?? '',
employmentType: json['employmentType'] ?? '',
title: json['title'] ?? '',
experienceLevel: json['experienceLevel'] ?? '',
backgroundPreferences: json['backgroundPreferences'],
startDate: json['startDate'] != null
? DateTime.parse(json['startDate'])
    : DateTime.now(),
location: json['location'] ?? '',
candidateLocation: json['candidateLocation'],
radius: json['radius']?.toDouble(),
relocationOptions: json['relocationOptions'] ?? '',
isShortlisted: json['isShortlisted'] ?? false,
createdAt: json['createdAt'] != null
? DateTime.parse(json['createdAt'])
    : DateTime.now(),
);
}
  );

  // Convert JobListing to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'employer': employer, // Ensure this is handled appropriately
      'projectName': projectName,
      'jobDescription': jobDescription,
      'industry': industry,
      'employmentType': employmentType,
      'title': title,
      'experienceLevel': experienceLevel,
      'backgroundPreferences': backgroundPreferences,
      'startDate': startDate.toIso8601String(),
      'location': location,
      'candidateLocation': candidateLocation,
      'radius': radius,
      'relocationOptions': relocationOptions,
    };
  }
}*/
