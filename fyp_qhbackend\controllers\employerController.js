// controllers/employerController.js
const EmployerProfile = require('../models/Employer');
const User = require('../models/User');
const Project = require('../models/Project');
const { deleteImage } = require('../utils/fileUpload');

// @desc    Get employer profile
// @route   GET /api/v1/employer/profile
// @access  Private (Employers only)
exports.getProfile = async (req, res) => {
  try {
    const profile = await EmployerProfile.findOne({ user: req.user.id })
      .populate({
        path: 'user',
        select: 'name email location isVerified createdAt'
      });

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Employer profile not found'
      });
    }

    res.status(200).json({
      success: true,
      data: profile
    });
  } catch (error) {
    console.error('Get Profile Error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching profile',
      error: error.message
    });
  }
};

// @desc    Update employer profile
// @route   PUT /api/v1/employer/profile
// @access  Private (Employers only)
exports.updateProfile = async (req, res) => {
  try {
    const {
      companyName,
      bio,
      linkedinUrl,
      website,
      phoneNumber,
      contactInfo,
      companySize,
      industry
    } = req.body;

    // Find existing profile
    let profile = await EmployerProfile.findOne({ user: req.user.id });

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Employer profile not found'
      });
    }

    // Update profile fields
    const updateFields = {};
    if (companyName !== undefined) updateFields.companyName = companyName;
    if (bio !== undefined) updateFields.bio = bio;
    if (linkedinUrl !== undefined) updateFields.linkedinUrl = linkedinUrl;
    if (website !== undefined) updateFields.website = website;
    if (phoneNumber !== undefined) updateFields.phoneNumber = phoneNumber;
    if (contactInfo !== undefined) updateFields.contactInfo = contactInfo;
    if (companySize !== undefined) updateFields.companySize = companySize;
    if (industry !== undefined) updateFields.industry = industry;

    // Update profile
    profile = await EmployerProfile.findByIdAndUpdate(
      profile._id,
      updateFields,
      { new: true, runValidators: true }
    ).populate({
      path: 'user',
      select: 'name email location isVerified createdAt'
    });

    res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      data: profile
    });
  } catch (error) {
    console.error('Update Profile Error:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating profile',
      error: error.message
    });
  }
};

// @desc    Upload profile picture
// @route   POST /api/v1/employer/profile/picture
// @access  Private (Employers only)
exports.uploadProfilePicture = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Please upload an image file'
      });
    }

    // Find employer profile
    const profile = await EmployerProfile.findOne({ user: req.user.id });

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Employer profile not found'
      });
    }

    // Delete old profile picture if exists
    if (profile.profilePicture) {
      await deleteImage(profile.profilePicture);
    }

    // Update profile with new picture URL
    profile.profilePicture = req.file.path;
    await profile.save();

    res.status(200).json({
      success: true,
      message: 'Profile picture uploaded successfully',
      data: {
        profilePicture: profile.profilePicture
      }
    });
  } catch (error) {
    console.error('Upload Profile Picture Error:', error);
    res.status(500).json({
      success: false,
      message: 'Error uploading profile picture',
      error: error.message
    });
  }
};

// @desc    Delete profile picture
// @route   DELETE /api/v1/employer/profile/picture
// @access  Private (Employers only)
exports.deleteProfilePicture = async (req, res) => {
  try {
    const profile = await EmployerProfile.findOne({ user: req.user.id });

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Employer profile not found'
      });
    }

    if (!profile.profilePicture) {
      return res.status(400).json({
        success: false,
        message: 'No profile picture to delete'
      });
    }

    // Delete image from cloudinary
    await deleteImage(profile.profilePicture);

    // Remove profile picture from database
    profile.profilePicture = '';
    await profile.save();

    res.status(200).json({
      success: true,
      message: 'Profile picture deleted successfully'
    });
  } catch (error) {
    console.error('Delete Profile Picture Error:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting profile picture',
      error: error.message
    });
  }
};

// @desc    Get employer dashboard stats
// @route   GET /api/v1/employer/dashboard
// @access  Private (Employers only)
exports.getDashboardStats = async (req, res) => {
  try {
    // Get total projects count
    const totalProjects = await Project.countDocuments({ employer: req.user.id });

    // Get active projects count
    const activeProjects = await Project.countDocuments({ 
      employer: req.user.id, 
      status: { $in: ['open', 'in-progress'] }
    });

    // Get completed projects count
    const completedProjects = await Project.countDocuments({ 
      employer: req.user.id, 
      status: 'completed'
    });

    // Get total applications received
    const projects = await Project.find({ employer: req.user.id });
    let totalApplications = 0;
    let pendingApplications = 0;
    let acceptedApplications = 0;

    projects.forEach(project => {
      if (project.acceptedBy && project.acceptedBy.length > 0) {
        totalApplications += project.acceptedBy.length;
        project.acceptedBy.forEach(app => {
          if (app.status === 'pending') pendingApplications++;
          if (app.status === 'accepted') acceptedApplications++;
        });
      }
    });

    res.status(200).json({
      success: true,
      data: {
        totalProjects,
        activeProjects,
        completedProjects,
        totalApplications,
        pendingApplications,
        acceptedApplications
      }
    });
  } catch (error) {
    console.error('Get Dashboard Stats Error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching dashboard stats',
      error: error.message
    });
  }
};
