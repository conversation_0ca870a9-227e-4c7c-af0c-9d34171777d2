// lib/screens/project_application_screen.dart
import 'package:flutter/material.dart';
import '../models/job_listing.dart';
import '../services/jobseeker_service.dart';

class ProjectApplicationScreen extends StatefulWidget {
  final JobListing project;

  const ProjectApplicationScreen({
    Key? key,
    required this.project,
  }) : super(key: key);

  @override
  State<ProjectApplicationScreen> createState() => _ProjectApplicationScreenState();
}

class _ProjectApplicationScreenState extends State<ProjectApplicationScreen> {
  final _formKey = GlobalKey<FormState>();
  final JobSeekerService _jobSeekerService = JobSeekerService();

  // Controllers
  final _coverLetterController = TextEditingController();
  final _proposedBudgetController = TextEditingController();
  final _estimatedDurationController = TextEditingController();

  bool _isSubmitting = false;
  List<Map<String, String>> _portfolioItems = [];

  @override
  void initState() {
    super.initState();
    // Pre-fill budget with project range
    _proposedBudgetController.text = widget.project.budget.min.toString();
  }

  @override
  void dispose() {
    _coverLetterController.dispose();
    _proposedBudgetController.dispose();
    _estimatedDurationController.dispose();
    super.dispose();
  }

  Future<void> _submitApplication() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final success = await _jobSeekerService.applyToProject(
        projectId: widget.project.id,
        coverLetter: _coverLetterController.text.trim(),
        proposedBudget: double.tryParse(_proposedBudgetController.text),
        estimatedDuration: _estimatedDurationController.text.trim().isEmpty 
            ? null 
            : _estimatedDurationController.text.trim(),
        portfolio: _portfolioItems.isEmpty ? null : _portfolioItems,
      );

      if (success) {
        if (mounted) {
          Navigator.pop(context, true); // Return true to indicate successful application
        }
      }
    } catch (e) {
      _showError('Failed to submit application: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  void _addPortfolioItem() {
    showDialog(
      context: context,
      builder: (context) => _PortfolioItemDialog(
        onAdd: (item) {
          setState(() {
            _portfolioItems.add(item);
          });
        },
      ),
    );
  }

  void _removePortfolioItem(int index) {
    setState(() {
      _portfolioItems.removeAt(index);
    });
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Apply to Project'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Project summary
              _buildProjectSummary(),
              const SizedBox(height: 24),

              // Cover letter
              _buildCoverLetterSection(),
              const SizedBox(height: 24),

              // Proposed budget
              _buildBudgetSection(),
              const SizedBox(height: 24),

              // Estimated duration
              _buildDurationSection(),
              const SizedBox(height: 24),

              // Portfolio items
              _buildPortfolioSection(),
              const SizedBox(height: 32),

              // Submit button
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProjectSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.project.title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'by ${widget.project.employerName}',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Budget',
                    '${widget.project.budget.min} - ${widget.project.budget.max} ${widget.project.budget.currency}',
                    Icons.attach_money,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Duration',
                    widget.project.duration,
                    Icons.schedule,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Location',
                    widget.project.location,
                    Icons.location_on,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Experience',
                    widget.project.experienceLevel.toUpperCase(),
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.blue),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCoverLetterSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Cover Letter *',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Explain why you\'re the perfect fit for this project',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _coverLetterController,
          decoration: const InputDecoration(
            hintText: 'Dear Hiring Manager,\n\nI am excited to apply for this project because...',
            border: OutlineInputBorder(),
            alignLabelWithHint: true,
          ),
          maxLines: 8,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Cover letter is required';
            }
            if (value.trim().length < 50) {
              return 'Cover letter should be at least 50 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildBudgetSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Proposed Budget (Optional)',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Project budget range: ${widget.project.budget.min} - ${widget.project.budget.max} ${widget.project.budget.currency}',
          style: const TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _proposedBudgetController,
          decoration: InputDecoration(
            labelText: 'Your proposed budget',
            hintText: 'Enter amount in ${widget.project.budget.currency}',
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.attach_money),
            suffixText: widget.project.budget.currency,
          ),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              final budget = double.tryParse(value);
              if (budget == null) {
                return 'Please enter a valid number';
              }
              if (budget < widget.project.budget.min || budget > widget.project.budget.max) {
                return 'Budget should be between ${widget.project.budget.min} and ${widget.project.budget.max}';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDurationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Estimated Duration (Optional)',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Expected project duration: ${widget.project.duration}',
          style: const TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _estimatedDurationController,
          decoration: const InputDecoration(
            labelText: 'Your estimated timeline',
            hintText: 'e.g., 2 weeks, 1 month, 3 months',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.schedule),
          ),
        ),
      ],
    );
  }

  Widget _buildPortfolioSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Portfolio Items (Optional)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: _addPortfolioItem,
              icon: const Icon(Icons.add),
              label: const Text('Add Item'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        const Text(
          'Showcase relevant work examples',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 12),
        if (_portfolioItems.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(Icons.work_outline, size: 48, color: Colors.grey[400]),
                  const SizedBox(height: 8),
                  Text(
                    'No portfolio items added',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          )
        else
          ...List.generate(_portfolioItems.length, (index) {
            final item = _portfolioItems[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: const Icon(Icons.work),
                title: Text(item['title'] ?? ''),
                subtitle: Text(item['description'] ?? ''),
                trailing: IconButton(
                  onPressed: () => _removePortfolioItem(index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                ),
              ),
            );
          }),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _submitApplication,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: _isSubmitting
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Submit Application',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }
}

class _PortfolioItemDialog extends StatefulWidget {
  final Function(Map<String, String>) onAdd;

  const _PortfolioItemDialog({required this.onAdd});

  @override
  State<_PortfolioItemDialog> createState() => _PortfolioItemDialogState();
}

class _PortfolioItemDialogState extends State<_PortfolioItemDialog> {
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _urlController = TextEditingController();

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Portfolio Item'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Title',
              hintText: 'e.g., Mobile App for Restaurant',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              hintText: 'Brief description of the project',
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _urlController,
            decoration: const InputDecoration(
              labelText: 'URL (Optional)',
              hintText: 'Link to live project or repository',
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_titleController.text.trim().isNotEmpty &&
                _descriptionController.text.trim().isNotEmpty) {
              widget.onAdd({
                'title': _titleController.text.trim(),
                'description': _descriptionController.text.trim(),
                'url': _urlController.text.trim(),
              });
              Navigator.pop(context);
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
          child: const Text('Add'),
        ),
      ],
    );
  }
}
