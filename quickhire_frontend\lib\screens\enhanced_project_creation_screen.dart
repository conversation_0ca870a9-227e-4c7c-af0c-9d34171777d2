// lib/screens/enhanced_project_creation_screen.dart
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/job_listing.dart';
import '../services/project_service.dart';

class EnhancedProjectCreationScreen extends StatefulWidget {
  static const String routeName = '/enhanced-project-creation';
  static const String id = 'EnhancedProjectCreationScreen';

  const EnhancedProjectCreationScreen({Key? key}) : super(key: key);

  @override
  State<EnhancedProjectCreationScreen> createState() => _EnhancedProjectCreationScreenState();
}

class _EnhancedProjectCreationScreenState extends State<EnhancedProjectCreationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();
  int _currentPage = 0;
  bool _isLoading = false;

  // Controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _skillsController = TextEditingController();
  final _locationController = TextEditingController();
  final _durationController = TextEditingController();
  final _minBudgetController = TextEditingController();
  final _maxBudgetController = TextEditingController();

  // Form data
  String _workType = 'remote';
  String _experienceLevel = 'entry';
  String _projectType = 'fixed-price';
  String _currency = 'USD';
  DateTime? _deadline;
  List<String> _selectedSkills = [];

  final ProjectService _projectService = ProjectService();

  // Options
  final List<String> _workTypes = ['remote', 'on-site', 'hybrid'];
  final List<String> _experienceLevels = ['entry', 'intermediate', 'expert'];
  final List<String> _projectTypes = ['fixed-price', 'hourly'];
  final List<String> _currencies = ['USD', 'EUR', 'GBP', 'PKR', 'INR'];
  final List<String> _skillOptions = [
    'Flutter', 'React', 'Node.js', 'Python', 'Java', 'JavaScript',
    'UI/UX Design', 'Graphic Design', 'Digital Marketing', 'Content Writing',
    'Data Analysis', 'Machine Learning', 'DevOps', 'Project Management'
  ];

  @override
  void initState() {
    super.initState();
    _loadSavedData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _skillsController.dispose();
    _locationController.dispose();
    _durationController.dispose();
    _minBudgetController.dispose();
    _maxBudgetController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadSavedData() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _titleController.text = prefs.getString('project_title') ?? '';
      _descriptionController.text = prefs.getString('project_description') ?? '';
      _locationController.text = prefs.getString('project_location') ?? '';
      _durationController.text = prefs.getString('project_duration') ?? '';
      _minBudgetController.text = prefs.getString('project_min_budget') ?? '';
      _maxBudgetController.text = prefs.getString('project_max_budget') ?? '';
      _workType = prefs.getString('project_work_type') ?? 'remote';
      _experienceLevel = prefs.getString('project_experience_level') ?? 'entry';
      _projectType = prefs.getString('project_type') ?? 'fixed-price';
      _currency = prefs.getString('project_currency') ?? 'USD';
      
      final skillsString = prefs.getString('project_skills');
      if (skillsString != null && skillsString.isNotEmpty) {
        _selectedSkills = skillsString.split(',');
      }
      
      final deadlineString = prefs.getString('project_deadline');
      if (deadlineString != null) {
        _deadline = DateTime.tryParse(deadlineString);
      }
    });
  }

  Future<void> _saveData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('project_title', _titleController.text);
    await prefs.setString('project_description', _descriptionController.text);
    await prefs.setString('project_location', _locationController.text);
    await prefs.setString('project_duration', _durationController.text);
    await prefs.setString('project_min_budget', _minBudgetController.text);
    await prefs.setString('project_max_budget', _maxBudgetController.text);
    await prefs.setString('project_work_type', _workType);
    await prefs.setString('project_experience_level', _experienceLevel);
    await prefs.setString('project_type', _projectType);
    await prefs.setString('project_currency', _currency);
    await prefs.setString('project_skills', _selectedSkills.join(','));
    if (_deadline != null) {
      await prefs.setString('project_deadline', _deadline!.toIso8601String());
    }
  }

  Future<void> _clearSavedData() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = [
      'project_title', 'project_description', 'project_location', 'project_duration',
      'project_min_budget', 'project_max_budget', 'project_work_type',
      'project_experience_level', 'project_type', 'project_currency',
      'project_skills', 'project_deadline'
    ];
    for (String key in keys) {
      await prefs.remove(key);
    }
  }

  void _nextPage() {
    if (_currentPage < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _selectDeadline() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _deadline ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        _deadline = picked;
      });
      await _saveData();
    }
  }

  void _toggleSkill(String skill) {
    setState(() {
      if (_selectedSkills.contains(skill)) {
        _selectedSkills.remove(skill);
      } else {
        _selectedSkills.add(skill);
      }
    });
    _saveData();
  }

  Future<void> _submitProject() async {
    if (!_formKey.currentState!.validate()) return;
    if (_deadline == null) {
      _showError('Please select a project deadline');
      return;
    }
    if (_selectedSkills.isEmpty) {
      _showError('Please select at least one skill');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final minBudget = double.tryParse(_minBudgetController.text) ?? 0;
      final maxBudget = double.tryParse(_maxBudgetController.text) ?? 0;

      if (minBudget >= maxBudget) {
        _showError('Maximum budget must be greater than minimum budget');
        return;
      }

      final project = JobListing(
        id: '',
        employer: '',
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        skills: _selectedSkills,
        location: _locationController.text.trim(),
        workType: _workType,
        budget: Budget(
          min: minBudget,
          max: maxBudget,
          currency: _currency,
        ),
        duration: _durationController.text.trim(),
        deadline: _deadline,
        experienceLevel: _experienceLevel,
        projectType: _projectType,
        status: 'open',
        applications: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _projectService.createProject(project);
      await _clearSavedData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Project created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.popUntil(context, ModalRoute.withName('/emp-dashboard'));
      }
    } catch (e) {
      _showError('Failed to create project: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Project'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Progress indicator
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: List.generate(3, (index) {
                return Expanded(
                  child: Container(
                    height: 4,
                    margin: EdgeInsets.only(right: index < 2 ? 8 : 0),
                    decoration: BoxDecoration(
                      color: index <= _currentPage ? Colors.amber : Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                );
              }),
            ),
          ),
          // Page content
          Expanded(
            child: Form(
              key: _formKey,
              child: PageView(
                controller: _pageController,
                onPageChanged: (page) {
                  setState(() {
                    _currentPage = page;
                  });
                },
                children: [
                  _buildBasicInfoPage(),
                  _buildDetailsPage(),
                  _buildBudgetAndDeadlinePage(),
                ],
              ),
            ),
          ),
          // Navigation buttons
          _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildBasicInfoPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Basic Information',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Project Title *',
              hintText: 'e.g., Mobile App Development',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Project title is required';
              }
              return null;
            },
            onChanged: (_) => _saveData(),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Project Description *',
              hintText: 'Describe your project in detail...',
              border: OutlineInputBorder(),
            ),
            maxLines: 4,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Project description is required';
              }
              if (value.trim().length < 50) {
                return 'Description should be at least 50 characters';
              }
              return null;
            },
            onChanged: (_) => _saveData(),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _locationController,
            decoration: const InputDecoration(
              labelText: 'Location *',
              hintText: 'e.g., New York, NY or Remote',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Location is required';
              }
              return null;
            },
            onChanged: (_) => _saveData(),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _workType,
            decoration: const InputDecoration(
              labelText: 'Work Type',
              border: OutlineInputBorder(),
            ),
            items: _workTypes.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type.toUpperCase()),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _workType = value!;
              });
              _saveData();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Project Details',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'Required Skills *',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _skillOptions.map((skill) {
              final isSelected = _selectedSkills.contains(skill);
              return FilterChip(
                label: Text(skill),
                selected: isSelected,
                onSelected: (_) => _toggleSkill(skill),
                selectedColor: Colors.amber.withValues(alpha: 0.3),
                checkmarkColor: Colors.amber[800],
              );
            }).toList(),
          ),
          if (_selectedSkills.isEmpty)
            const Padding(
              padding: EdgeInsets.only(top: 8),
              child: Text(
                'Please select at least one skill',
                style: TextStyle(color: Colors.red, fontSize: 12),
              ),
            ),
          const SizedBox(height: 24),
          DropdownButtonFormField<String>(
            value: _experienceLevel,
            decoration: const InputDecoration(
              labelText: 'Experience Level Required',
              border: OutlineInputBorder(),
            ),
            items: _experienceLevels.map((level) {
              return DropdownMenuItem(
                value: level,
                child: Text(level.toUpperCase()),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _experienceLevel = value!;
              });
              _saveData();
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _projectType,
            decoration: const InputDecoration(
              labelText: 'Project Type',
              border: OutlineInputBorder(),
            ),
            items: _projectTypes.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type.replaceAll('-', ' ').toUpperCase()),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _projectType = value!;
              });
              _saveData();
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _durationController,
            decoration: const InputDecoration(
              labelText: 'Estimated Duration *',
              hintText: 'e.g., 2-3 months, 6 weeks',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Duration is required';
              }
              return null;
            },
            onChanged: (_) => _saveData(),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetAndDeadlinePage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Budget & Timeline',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _currency,
                  decoration: const InputDecoration(
                    labelText: 'Currency',
                    border: OutlineInputBorder(),
                  ),
                  items: _currencies.map((currency) {
                    return DropdownMenuItem(
                      value: currency,
                      child: Text(currency),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _currency = value!;
                    });
                    _saveData();
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _minBudgetController,
                  decoration: const InputDecoration(
                    labelText: 'Minimum Budget *',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Required';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Invalid number';
                    }
                    return null;
                  },
                  onChanged: (_) => _saveData(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _maxBudgetController,
                  decoration: const InputDecoration(
                    labelText: 'Maximum Budget *',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Required';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Invalid number';
                    }
                    return null;
                  },
                  onChanged: (_) => _saveData(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          const Text(
            'Project Deadline *',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          InkWell(
            onTap: _selectDeadline,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _deadline == null
                        ? 'Select deadline'
                        : '${_deadline!.day}/${_deadline!.month}/${_deadline!.year}',
                    style: TextStyle(
                      color: _deadline == null ? Colors.grey : Colors.black,
                    ),
                  ),
                  const Icon(Icons.calendar_today),
                ],
              ),
            ),
          ),
          if (_deadline == null)
            const Padding(
              padding: EdgeInsets.only(top: 8),
              child: Text(
                'Please select a deadline',
                style: TextStyle(color: Colors.red, fontSize: 12),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          if (_currentPage > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousPage,
                child: const Text('Previous'),
              ),
            ),
          if (_currentPage > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : _currentPage == 2
                      ? _submitProject
                      : _nextPage,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(_currentPage == 2 ? 'Create Project' : 'Next'),
            ),
          ),
        ],
      ),
    );
  }
}
