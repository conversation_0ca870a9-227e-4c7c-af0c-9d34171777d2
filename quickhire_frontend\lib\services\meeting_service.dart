// lib/services/meeting_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import '../models/message_model.dart';

class MeetingService {
  static const String baseUrl = AppConfig.meetingsEndpoint;

  // Get headers with auth token
  Future<Map<String, String>> get _headers async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token') ?? '';
    
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Schedule a new meeting
  Future<Meeting> scheduleMeeting({
    required String title,
    required String description,
    required DateTime scheduledAt,
    required int durationMinutes,
    required List<String> participants,
    String? conversationId,
  }) async {
    try {
      final headers = await _headers;
      final meetingData = {
        'title': title,
        'description': description,
        'scheduledAt': scheduledAt.toIso8601String(),
        'durationMinutes': durationMinutes,
        'participants': participants,
        if (conversationId != null) 'conversationId': conversationId,
      };

      final response = await http.post(
        Uri.parse(baseUrl),
        headers: headers,
        body: json.encode(meetingData),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return Meeting.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to schedule meeting');
      }
    } catch (e) {
      throw Exception('Error scheduling meeting: $e');
    }
  }

  // Get user's meetings
  Future<List<Meeting>> getMeetings({
    String? status,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final headers = await _headers;
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (status != null) queryParams['status'] = status;
      if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

      final uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final meetingsJson = data['data'] as List;
        return meetingsJson.map((json) => Meeting.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load meetings: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching meetings: $e');
    }
  }

  // Get meeting by ID
  Future<Meeting> getMeeting(String meetingId) async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/$meetingId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Meeting.fromJson(data['data']);
      } else {
        throw Exception('Failed to load meeting: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching meeting: $e');
    }
  }

  // Update meeting
  Future<Meeting> updateMeeting(String meetingId, {
    String? title,
    String? description,
    DateTime? scheduledAt,
    int? durationMinutes,
    String? status,
  }) async {
    try {
      final headers = await _headers;
      final updateData = <String, dynamic>{};
      
      if (title != null) updateData['title'] = title;
      if (description != null) updateData['description'] = description;
      if (scheduledAt != null) updateData['scheduledAt'] = scheduledAt.toIso8601String();
      if (durationMinutes != null) updateData['durationMinutes'] = durationMinutes;
      if (status != null) updateData['status'] = status;

      final response = await http.put(
        Uri.parse('$baseUrl/$meetingId'),
        headers: headers,
        body: json.encode(updateData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Meeting.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to update meeting');
      }
    } catch (e) {
      throw Exception('Error updating meeting: $e');
    }
  }

  // Cancel meeting
  Future<bool> cancelMeeting(String meetingId, {String? reason}) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/$meetingId/cancel'),
        headers: headers,
        body: json.encode({
          'reason': reason ?? 'Meeting cancelled',
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error cancelling meeting: $e');
    }
  }

  // Start meeting
  Future<Meeting> startMeeting(String meetingId) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/$meetingId/start'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Meeting.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to start meeting');
      }
    } catch (e) {
      throw Exception('Error starting meeting: $e');
    }
  }

  // End meeting
  Future<Meeting> endMeeting(String meetingId) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/$meetingId/end'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Meeting.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to end meeting');
      }
    } catch (e) {
      throw Exception('Error ending meeting: $e');
    }
  }

  // Get available time slots
  Future<List<DateTime>> getAvailableTimeSlots({
    required List<String> participants,
    required DateTime startDate,
    required DateTime endDate,
    required int durationMinutes,
  }) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse('$baseUrl/availability'),
        headers: headers,
        body: json.encode({
          'participants': participants,
          'startDate': startDate.toIso8601String(),
          'endDate': endDate.toIso8601String(),
          'durationMinutes': durationMinutes,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final slotsJson = data['data'] as List;
        return slotsJson.map((slot) => DateTime.parse(slot)).toList();
      } else {
        throw Exception('Failed to get available slots: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching available slots: $e');
    }
  }

  // Generate meeting link (for video calls)
  Future<String> generateMeetingLink(String meetingId) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse('$baseUrl/$meetingId/link'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['data']['meetingUrl'];
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to generate meeting link');
      }
    } catch (e) {
      throw Exception('Error generating meeting link: $e');
    }
  }

  // Get mock meetings for development
  List<Meeting> getMockMeetings() {
    final now = DateTime.now();
    return [
      Meeting(
        id: '1',
        title: 'Project Discussion - Mobile App',
        description: 'Initial discussion about the mobile app requirements and timeline',
        scheduledAt: now.add(const Duration(days: 1, hours: 2)),
        durationMinutes: 60,
        meetingUrl: 'https://meet.google.com/abc-defg-hij',
        meetingId: 'abc-defg-hij',
        status: 'scheduled',
        organizer: 'emp1',
        participants: ['emp1', 'js1'],
        createdAt: now.subtract(const Duration(hours: 2)),
      ),
      Meeting(
        id: '2',
        title: 'Technical Review',
        description: 'Review of the proposed technical architecture and approach',
        scheduledAt: now.add(const Duration(days: 3, hours: 10)),
        durationMinutes: 45,
        status: 'scheduled',
        organizer: 'js1',
        participants: ['emp1', 'js1'],
        createdAt: now.subtract(const Duration(hours: 5)),
      ),
      Meeting(
        id: '3',
        title: 'Project Kickoff',
        description: 'Official project kickoff meeting',
        scheduledAt: now.subtract(const Duration(days: 1)),
        durationMinutes: 30,
        meetingUrl: 'https://meet.google.com/xyz-uvwx-yz',
        meetingId: 'xyz-uvwx-yz',
        status: 'completed',
        organizer: 'emp1',
        participants: ['emp1', 'js1'],
        createdAt: now.subtract(const Duration(days: 2)),
      ),
    ];
  }

  // Get mock available time slots
  List<DateTime> getMockAvailableTimeSlots() {
    final now = DateTime.now();
    final tomorrow = now.add(const Duration(days: 1));
    
    return [
      DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 9, 0),
      DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 10, 30),
      DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 14, 0),
      DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 15, 30),
      DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 16, 0),
    ];
  }

  // Quick schedule meeting with common durations
  Future<Meeting> quickScheduleMeeting({
    required String title,
    required DateTime scheduledAt,
    required List<String> participants,
    String description = '',
    int durationMinutes = 30,
  }) async {
    return scheduleMeeting(
      title: title,
      description: description,
      scheduledAt: scheduledAt,
      durationMinutes: durationMinutes,
      participants: participants,
    );
  }

  // Reschedule meeting
  Future<Meeting> rescheduleMeeting(String meetingId, DateTime newDateTime) async {
    return updateMeeting(
      meetingId,
      scheduledAt: newDateTime,
    );
  }

  // Get today's meetings
  Future<List<Meeting>> getTodaysMeetings() async {
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    return getMeetings(
      startDate: startOfDay,
      endDate: endOfDay,
    );
  }

  // Get upcoming meetings
  Future<List<Meeting>> getUpcomingMeetings({int days = 7}) async {
    final now = DateTime.now();
    final endDate = now.add(Duration(days: days));
    
    return getMeetings(
      status: 'scheduled',
      startDate: now,
      endDate: endDate,
    );
  }
}
