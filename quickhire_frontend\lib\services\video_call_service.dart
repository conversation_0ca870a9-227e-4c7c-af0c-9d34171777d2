// lib/services/video_call_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../config/app_config.dart';

enum VideoCallProvider {
  zoom,
  googleMeet,
  microsoftTeams,
  jitsi
}

class VideoCallService {
  static const String baseUrl = AppConfig.videoCallEndpoint;

  // Get headers with auth token
  Future<Map<String, String>> get _headers async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token') ?? '';
    
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Create video call room
  Future<Map<String, dynamic>> createVideoCall({
    required String meetingId,
    required String title,
    required DateTime scheduledAt,
    required int durationMinutes,
    required List<String> participants,
    VideoCallProvider provider = VideoCallProvider.jitsi,
  }) async {
    try {
      final headers = await _headers;
      final callData = {
        'meetingId': meetingId,
        'title': title,
        'scheduledAt': scheduledAt.toIso8601String(),
        'durationMinutes': durationMinutes,
        'participants': participants,
        'provider': provider.toString().split('.').last,
      };

      final response = await http.post(
        Uri.parse('$baseUrl/create'),
        headers: headers,
        body: json.encode(callData),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return data['data'];
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to create video call');
      }
    } catch (e) {
      throw Exception('Error creating video call: $e');
    }
  }

  // Generate Jitsi Meet URL (free option)
  String generateJitsiMeetUrl(String meetingId, String meetingTitle) {
    final roomName = '${meetingId}_${meetingTitle.replaceAll(' ', '_')}';
    return 'https://meet.jit.si/$roomName';
  }

  // Generate Google Meet URL (requires Google Workspace)
  Future<String> generateGoogleMeetUrl(String meetingId) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse('$baseUrl/google-meet'),
        headers: headers,
        body: json.encode({'meetingId': meetingId}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['meetingUrl'];
      } else {
        throw Exception('Failed to create Google Meet link');
      }
    } catch (e) {
      // Fallback to Jitsi if Google Meet fails
      return generateJitsiMeetUrl(meetingId, 'QuickHire Meeting');
    }
  }

  // Generate Zoom meeting URL (requires Zoom API)
  Future<String> generateZoomMeetingUrl({
    required String meetingId,
    required String title,
    required DateTime scheduledAt,
    required int durationMinutes,
  }) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse('$baseUrl/zoom'),
        headers: headers,
        body: json.encode({
          'meetingId': meetingId,
          'title': title,
          'scheduledAt': scheduledAt.toIso8601String(),
          'durationMinutes': durationMinutes,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['joinUrl'];
      } else {
        throw Exception('Failed to create Zoom meeting');
      }
    } catch (e) {
      // Fallback to Jitsi if Zoom fails
      return generateJitsiMeetUrl(meetingId, title);
    }
  }

  // Join video call
  Future<bool> joinVideoCall(String meetingUrl) async {
    try {
      final uri = Uri.parse(meetingUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        throw Exception('Cannot launch video call URL');
      }
    } catch (e) {
      throw Exception('Error joining video call: $e');
    }
  }

  // Start meeting (update status to ongoing)
  Future<bool> startMeeting(String meetingId) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/$meetingId/start'),
        headers: headers,
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error starting meeting: $e');
    }
  }

  // End meeting (update status to completed)
  Future<bool> endMeeting(String meetingId) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/$meetingId/end'),
        headers: headers,
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error ending meeting: $e');
    }
  }

  // Get meeting recording (if available)
  Future<String?> getMeetingRecording(String meetingId) async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/$meetingId/recording'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['recordingUrl'];
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Test video call connection
  Future<bool> testVideoCallConnection() async {
    try {
      final testUrl = generateJitsiMeetUrl('test_${DateTime.now().millisecondsSinceEpoch}', 'Connection Test');
      final uri = Uri.parse(testUrl);
      return await canLaunchUrl(uri);
    } catch (e) {
      return false;
    }
  }

  // Get supported video call providers
  List<VideoCallProvider> getSupportedProviders() {
    return [
      VideoCallProvider.jitsi, // Always available (free)
      VideoCallProvider.googleMeet, // Requires Google Workspace
      VideoCallProvider.zoom, // Requires Zoom API
      VideoCallProvider.microsoftTeams, // Requires Microsoft 365
    ];
  }

  // Get provider display name
  String getProviderDisplayName(VideoCallProvider provider) {
    switch (provider) {
      case VideoCallProvider.jitsi:
        return 'Jitsi Meet (Free)';
      case VideoCallProvider.googleMeet:
        return 'Google Meet';
      case VideoCallProvider.zoom:
        return 'Zoom';
      case VideoCallProvider.microsoftTeams:
        return 'Microsoft Teams';
    }
  }

  // Create meeting with automatic provider selection
  Future<Map<String, dynamic>> createMeetingWithBestProvider({
    required String meetingId,
    required String title,
    required DateTime scheduledAt,
    required int durationMinutes,
    required List<String> participants,
  }) async {
    // Try providers in order of preference
    final providers = [
      VideoCallProvider.jitsi, // Free and reliable
      VideoCallProvider.googleMeet,
      VideoCallProvider.zoom,
    ];

    for (final provider in providers) {
      try {
        String meetingUrl;
        
        switch (provider) {
          case VideoCallProvider.jitsi:
            meetingUrl = generateJitsiMeetUrl(meetingId, title);
            break;
          case VideoCallProvider.googleMeet:
            meetingUrl = await generateGoogleMeetUrl(meetingId);
            break;
          case VideoCallProvider.zoom:
            meetingUrl = await generateZoomMeetingUrl(
              meetingId: meetingId,
              title: title,
              scheduledAt: scheduledAt,
              durationMinutes: durationMinutes,
            );
            break;
          default:
            continue;
        }

        return {
          'meetingUrl': meetingUrl,
          'provider': provider.toString().split('.').last,
          'meetingId': meetingId,
          'password': provider == VideoCallProvider.jitsi ? null : _generateMeetingPassword(),
        };
      } catch (e) {
        // Continue to next provider if current one fails
        continue;
      }
    }

    throw Exception('Failed to create meeting with any provider');
  }

  // Generate secure meeting password
  String _generateMeetingPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return List.generate(6, (index) => chars[random % chars.length]).join();
  }

  // Mock video call data for development
  Map<String, dynamic> getMockVideoCallData(String meetingId, String title) {
    return {
      'meetingUrl': generateJitsiMeetUrl(meetingId, title),
      'provider': 'jitsi',
      'meetingId': meetingId,
      'password': null,
      'instructions': 'Click the link to join the video call. No account required.',
    };
  }
}
