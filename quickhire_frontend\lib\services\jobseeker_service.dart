// lib/services/jobseeker_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import '../models/jobseeker_model.dart';
import '../models/job_listing.dart';

class JobSeekerService {
  static const String baseUrl = '${AppConfig.apiBaseUrl}/api/v1/jobseekers';

  // Get headers with auth token
  Future<Map<String, String>> get _headers async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token') ?? '';
    
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Job Seeker Sign Up
  Future<JobSeekerModel> signUp({
    required JobSeekerModel jobSeeker,
    required String password,
  }) async {
    try {
      final signUpData = {
        ...jobSeeker.toJson(),
        'password': password,
        'userType': 'jobseeker',
      };

      final response = await http.post(
        Uri.parse('${AppConfig.authEndpoint}/signup'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(signUpData),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return JobSeekerModel.fromJson(data['user']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to create account');
      }
    } catch (e) {
      throw Exception('Error creating account: $e');
    }
  }

  // Get job seeker profile
  Future<JobSeekerModel> getProfile() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/profile'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return JobSeekerModel.fromJson(data['data']);
      } else {
        throw Exception('Failed to load profile: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching profile: $e');
    }
  }

  // Update job seeker profile
  Future<JobSeekerModel> updateProfile(JobSeekerModel jobSeeker) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/profile'),
        headers: headers,
        body: json.encode(jobSeeker.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return JobSeekerModel.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to update profile');
      }
    } catch (e) {
      throw Exception('Error updating profile: $e');
    }
  }

  // Get available projects for job seeker
  Future<List<JobListing>> getAvailableProjects({
    int page = 1,
    int limit = 20,
    String? location,
    double? minBudget,
    double? maxBudget,
    String? experienceLevel,
    List<String>? skills,
  }) async {
    try {
      final headers = await _headers;
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
        'status': 'open',
      };

      if (location != null) queryParams['location'] = location;
      if (minBudget != null) queryParams['minBudget'] = minBudget.toString();
      if (maxBudget != null) queryParams['maxBudget'] = maxBudget.toString();
      if (experienceLevel != null) queryParams['experienceLevel'] = experienceLevel;
      if (skills != null && skills.isNotEmpty) queryParams['skills'] = skills.join(',');

      final uri = Uri.parse(AppConfig.projectsEndpoint).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final projectsJson = data['data'] as List;
        return projectsJson.map((json) => JobListing.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load projects: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching projects: $e');
    }
  }

  // Apply to a project
  Future<bool> applyToProject({
    required String projectId,
    required String coverLetter,
    double? proposedBudget,
    String? estimatedDuration,
    List<Map<String, String>>? portfolio,
  }) async {
    try {
      final headers = await _headers;
      final applicationData = {
        'coverLetter': coverLetter,
        if (proposedBudget != null) 'proposedBudget': proposedBudget,
        if (estimatedDuration != null) 'estimatedDuration': estimatedDuration,
        if (portfolio != null) 'portfolio': portfolio,
      };

      final response = await http.post(
        Uri.parse('${AppConfig.projectsEndpoint}/$projectId/apply'),
        headers: headers,
        body: json.encode(applicationData),
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to apply to project');
      }
    } catch (e) {
      throw Exception('Error applying to project: $e');
    }
  }

  // Get job seeker's applications
  Future<List<JobSeekerApplication>> getMyApplications() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/applications'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final applicationsJson = data['data'] as List;
        return applicationsJson.map((json) => JobSeekerApplication.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load applications: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching applications: $e');
    }
  }

  // Withdraw application
  Future<bool> withdrawApplication(String applicationId) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/applications/$applicationId/withdraw'),
        headers: headers,
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error withdrawing application: $e');
    }
  }

  // Update availability status
  Future<bool> updateAvailability(bool isAvailable) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/availability'),
        headers: headers,
        body: json.encode({'isAvailable': isAvailable}),
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error updating availability: $e');
    }
  }

  // Submit review for completed project
  Future<bool> submitReview({
    required String projectId,
    required double rating,
    required String comment,
  }) async {
    try {
      final headers = await _headers;
      final reviewData = {
        'rating': rating,
        'comment': comment,
        'reviewType': 'employer', // Job seeker reviewing employer
      };

      final response = await http.post(
        Uri.parse('${AppConfig.projectsEndpoint}/$projectId/reviews'),
        headers: headers,
        body: json.encode(reviewData),
      );

      if (response.statusCode == 201) {
        return true;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to submit review');
      }
    } catch (e) {
      throw Exception('Error submitting review: $e');
    }
  }

  // Get project details
  Future<JobListing> getProjectDetails(String projectId) async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('${AppConfig.projectsEndpoint}/$projectId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return JobListing.fromJson(data['data']);
      } else {
        throw Exception('Failed to load project details: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching project details: $e');
    }
  }

  // Get mock data for development
  List<JobListing> getMockProjects() {
    return [
      JobListing(
        id: '1',
        title: 'Mobile App Development',
        description: 'Looking for an experienced Flutter developer to build a cross-platform mobile application for our startup.',
        skills: ['Flutter', 'Dart', 'Firebase', 'REST APIs'],
        budget: Budget(min: 2000, max: 5000, currency: 'USD'),
        duration: '2-3 months',
        location: 'Remote',
        workType: 'remote',
        experienceLevel: 'intermediate',
        projectType: 'fixed',
        deadline: DateTime.now().add(const Duration(days: 90)),
        employerId: 'emp1',
        employerName: 'Tech Startup Inc.',
        status: 'open',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        views: 45,
        applicationsCount: 8,
      ),
      JobListing(
        id: '2',
        title: 'UI/UX Design for E-commerce',
        description: 'Need a creative UI/UX designer to redesign our e-commerce platform with modern, user-friendly interface.',
        skills: ['UI/UX Design', 'Figma', 'Adobe XD', 'Prototyping'],
        budget: Budget(min: 1500, max: 3000, currency: 'USD'),
        duration: '1-2 months',
        location: 'New York, NY',
        workType: 'hybrid',
        experienceLevel: 'intermediate',
        projectType: 'fixed',
        deadline: DateTime.now().add(const Duration(days: 60)),
        employerId: 'emp2',
        employerName: 'E-commerce Solutions',
        status: 'open',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now(),
        views: 32,
        applicationsCount: 12,
      ),
      JobListing(
        id: '3',
        title: 'Full Stack Web Development',
        description: 'Seeking a full-stack developer to build a comprehensive web application with React frontend and Node.js backend.',
        skills: ['React', 'Node.js', 'MongoDB', 'Express.js', 'JavaScript'],
        budget: Budget(min: 3000, max: 7000, currency: 'USD'),
        duration: '3-4 months',
        location: 'Remote',
        workType: 'remote',
        experienceLevel: 'expert',
        projectType: 'fixed',
        deadline: DateTime.now().add(const Duration(days: 120)),
        employerId: 'emp3',
        employerName: 'Digital Agency',
        status: 'open',
        createdAt: DateTime.now().subtract(const Duration(hours: 12)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 6)),
        views: 67,
        applicationsCount: 15,
      ),
    ];
  }

  // Get mock applications for development
  List<JobSeekerApplication> getMockApplications() {
    return [
      JobSeekerApplication(
        id: '1',
        projectId: '1',
        projectTitle: 'Mobile App Development',
        employerId: 'emp1',
        employerName: 'Tech Startup Inc.',
        status: 'pending',
        appliedAt: DateTime.now().subtract(const Duration(hours: 6)),
        coverLetter: 'I am very interested in this mobile app project and believe my Flutter expertise makes me a perfect fit.',
        proposedBudget: 4000.0,
        estimatedDuration: '2.5 months',
      ),
      JobSeekerApplication(
        id: '2',
        projectId: '2',
        projectTitle: 'UI/UX Design for E-commerce',
        employerId: 'emp2',
        employerName: 'E-commerce Solutions',
        status: 'accepted',
        appliedAt: DateTime.now().subtract(const Duration(days: 2)),
        coverLetter: 'I have extensive experience in e-commerce UI/UX design and would love to help redesign your platform.',
        proposedBudget: 2500.0,
        estimatedDuration: '6 weeks',
        reviewedAt: DateTime.now().subtract(const Duration(hours: 12)),
      ),
      JobSeekerApplication(
        id: '3',
        projectId: '3',
        projectTitle: 'Full Stack Web Development',
        employerId: 'emp3',
        employerName: 'Digital Agency',
        status: 'rejected',
        appliedAt: DateTime.now().subtract(const Duration(days: 3)),
        coverLetter: 'I am a full-stack developer with experience in React and Node.js.',
        proposedBudget: 5500.0,
        estimatedDuration: '3 months',
        reviewedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];
  }
}
