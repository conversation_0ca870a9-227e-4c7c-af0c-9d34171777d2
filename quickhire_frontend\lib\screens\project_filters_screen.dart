// lib/screens/project_filters_screen.dart
import 'package:flutter/material.dart';
import 'project_discovery_screen.dart';

class ProjectFiltersScreen extends StatefulWidget {
  final ProjectFilters currentFilters;

  const ProjectFiltersScreen({
    Key? key,
    required this.currentFilters,
  }) : super(key: key);

  @override
  State<ProjectFiltersScreen> createState() => _ProjectFiltersScreenState();
}

class _ProjectFiltersScreenState extends State<ProjectFiltersScreen> {
  late ProjectFilters _filters;
  final _locationController = TextEditingController();
  final _minBudgetController = TextEditingController();
  final _maxBudgetController = TextEditingController();

  final List<String> _experienceLevels = ['entry', 'intermediate', 'expert'];
  final List<String> _skillOptions = [
    'Flutter', 'React', 'Node.js', 'Python', 'Java', 'JavaScript',
    'UI/UX Design', 'Graphic Design', 'Digital Marketing', 'Content Writing',
    'Data Analysis', 'Machine Learning', 'DevOps', 'Project Management',
    'Mobile Development', 'Web Development', 'Database Management',
    'Cloud Computing', 'Cybersecurity', 'Quality Assurance'
  ];

  @override
  void initState() {
    super.initState();
    _filters = ProjectFilters(
      location: widget.currentFilters.location,
      minBudget: widget.currentFilters.minBudget,
      maxBudget: widget.currentFilters.maxBudget,
      experienceLevel: widget.currentFilters.experienceLevel,
      skills: widget.currentFilters.skills != null 
          ? List.from(widget.currentFilters.skills!) 
          : [],
    );

    _locationController.text = _filters.location ?? '';
    _minBudgetController.text = _filters.minBudget?.toString() ?? '';
    _maxBudgetController.text = _filters.maxBudget?.toString() ?? '';
  }

  @override
  void dispose() {
    _locationController.dispose();
    _minBudgetController.dispose();
    _maxBudgetController.dispose();
    super.dispose();
  }

  void _applyFilters() {
    // Update filters from form
    _filters = _filters.copyWith(
      location: _locationController.text.trim().isEmpty 
          ? null 
          : _locationController.text.trim(),
      minBudget: _minBudgetController.text.trim().isEmpty 
          ? null 
          : double.tryParse(_minBudgetController.text.trim()),
      maxBudget: _maxBudgetController.text.trim().isEmpty 
          ? null 
          : double.tryParse(_maxBudgetController.text.trim()),
    );

    Navigator.pop(context, _filters);
  }

  void _clearFilters() {
    setState(() {
      _filters = ProjectFilters();
      _locationController.clear();
      _minBudgetController.clear();
      _maxBudgetController.clear();
    });
  }

  void _toggleSkill(String skill) {
    setState(() {
      _filters.skills ??= [];
      if (_filters.skills!.contains(skill)) {
        _filters.skills!.remove(skill);
      } else {
        _filters.skills!.add(skill);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Filter Projects'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _clearFilters,
            child: const Text(
              'Clear All',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Location filter
            _buildLocationSection(),
            const SizedBox(height: 24),

            // Budget filter
            _buildBudgetSection(),
            const SizedBox(height: 24),

            // Experience level filter
            _buildExperienceSection(),
            const SizedBox(height: 24),

            // Skills filter
            _buildSkillsSection(),
            const SizedBox(height: 32),

            // Apply button
            _buildApplyButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Location',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _locationController,
          decoration: const InputDecoration(
            labelText: 'Location',
            hintText: 'e.g., Remote, New York, London',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.location_on),
          ),
        ),
      ],
    );
  }

  Widget _buildBudgetSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Budget Range (USD)',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _minBudgetController,
                decoration: const InputDecoration(
                  labelText: 'Min Budget',
                  hintText: '1000',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _maxBudgetController,
                decoration: const InputDecoration(
                  labelText: 'Max Budget',
                  hintText: '5000',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildExperienceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Experience Level',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            // Add "Any" option
            FilterChip(
              label: const Text('Any'),
              selected: _filters.experienceLevel == null,
              onSelected: (selected) {
                setState(() {
                  _filters = _filters.copyWith(experienceLevel: null);
                });
              },
              selectedColor: Colors.blue.withValues(alpha: 0.3),
              checkmarkColor: Colors.blue[800],
            ),
            ..._experienceLevels.map((level) {
              return FilterChip(
                label: Text(level.toUpperCase()),
                selected: _filters.experienceLevel == level,
                onSelected: (selected) {
                  setState(() {
                    _filters = _filters.copyWith(
                      experienceLevel: selected ? level : null,
                    );
                  });
                },
                selectedColor: Colors.blue.withValues(alpha: 0.3),
                checkmarkColor: Colors.blue[800],
              );
            }).toList(),
          ],
        ),
      ],
    );
  }

  Widget _buildSkillsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Required Skills',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Select skills you have experience with',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _skillOptions.map((skill) {
            final isSelected = _filters.skills?.contains(skill) ?? false;
            return FilterChip(
              label: Text(skill),
              selected: isSelected,
              onSelected: (_) => _toggleSkill(skill),
              selectedColor: Colors.blue.withValues(alpha: 0.3),
              checkmarkColor: Colors.blue[800],
            );
          }).toList(),
        ),
        if (_filters.skills != null && _filters.skills!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Text(
              '${_filters.skills!.length} skills selected',
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildApplyButton() {
    return Column(
      children: [
        // Filter summary
        if (_filters.hasActiveFilters)
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Active Filters:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(height: 8),
                ..._buildFilterSummary(),
              ],
            ),
          ),

        // Apply button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: Text(
              _filters.hasActiveFilters ? 'Apply Filters' : 'Show All Projects',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildFilterSummary() {
    final summary = <Widget>[];

    if (_filters.location != null && _filters.location!.isNotEmpty) {
      summary.add(Text('• Location: ${_filters.location}'));
    }

    if (_filters.minBudget != null || _filters.maxBudget != null) {
      String budgetText = '• Budget: ';
      if (_filters.minBudget != null && _filters.maxBudget != null) {
        budgetText += '\$${_filters.minBudget} - \$${_filters.maxBudget}';
      } else if (_filters.minBudget != null) {
        budgetText += 'Min \$${_filters.minBudget}';
      } else {
        budgetText += 'Max \$${_filters.maxBudget}';
      }
      summary.add(Text(budgetText));
    }

    if (_filters.experienceLevel != null) {
      summary.add(Text('• Experience: ${_filters.experienceLevel!.toUpperCase()}'));
    }

    if (_filters.skills != null && _filters.skills!.isNotEmpty) {
      summary.add(Text('• Skills: ${_filters.skills!.length} selected'));
    }

    return summary;
  }
}
