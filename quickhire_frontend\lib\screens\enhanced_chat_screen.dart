// lib/screens/enhanced_chat_screen.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import '../models/message_model.dart';
import '../services/enhanced_messaging_service.dart';
import '../services/meeting_service.dart';
import '../services/video_call_service.dart';
import 'meeting_scheduling_screen.dart';

class EnhancedChatScreen extends StatefulWidget {
  static const String routeName = '/enhanced-chat';
  static const String id = 'EnhancedChatScreen';

  final ChatConversation conversation;

  const EnhancedChatScreen({
    Key? key,
    required this.conversation,
  }) : super(key: key);

  @override
  State<EnhancedChatScreen> createState() => _EnhancedChatScreenState();
}

class _EnhancedChatScreenState extends State<EnhancedChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final EnhancedMessagingService _messagingService = EnhancedMessagingService();
  final MeetingService _meetingService = MeetingService();
  final VideoCallService _videoCallService = VideoCallService();
  final ImagePicker _imagePicker = ImagePicker();

  List<EnhancedMessage> _messages = [];
  bool _isLoading = true;
  bool _isSending = false;
  bool _isTyping = false;
  String? _otherUserTyping;
  EnhancedMessage? _replyToMessage;

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messagingService.leaveConversation(widget.conversation.id);
    _messagingService.disconnectSocket();
    super.dispose();
  }

  Future<void> _initializeChat() async {
    try {
      // Initialize socket connection
      await _messagingService.initializeSocket();
      
      // Set up message listeners
      _messagingService.onMessageReceived = (message) {
        setState(() {
          _messages.insert(0, message);
        });
        _scrollToBottom();
      };

      _messagingService.onUserTyping = (userId) {
        if (userId != widget.conversation.getOtherUserId('current_user_id')) return;
        setState(() {
          _otherUserTyping = widget.conversation.getOtherUserName('current_user_id');
        });
      };

      _messagingService.onUserStoppedTyping = (userId) {
        if (userId != widget.conversation.getOtherUserId('current_user_id')) return;
        setState(() {
          _otherUserTyping = null;
        });
      };

      // Join conversation
      _messagingService.joinConversation(widget.conversation.id);

      // Load messages
      await _loadMessages();
    } catch (e) {
      _showError('Failed to initialize chat: $e');
    }
  }

  Future<void> _loadMessages() async {
    try {
      // For development, use mock data
      final messages = _messagingService.getMockMessages();
      setState(() {
        _messages = messages;
        _isLoading = false;
      });
      _scrollToBottom();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showError('Failed to load messages: $e');
    }
  }

  Future<void> _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty || _isSending) return;

    setState(() {
      _isSending = true;
    });

    try {
      _messageController.clear();
      
      // Create temporary message for immediate UI update
      final tempMessage = EnhancedMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        senderId: 'current_user_id',
        senderName: 'You',
        receiverId: widget.conversation.getOtherUserId('current_user_id'),
        content: content,
        type: MessageType.text,
        status: MessageStatus.sending,
        timestamp: DateTime.now(),
        replyToId: _replyToMessage?.id,
        replyToMessage: _replyToMessage,
      );

      setState(() {
        _messages.insert(0, tempMessage);
        _replyToMessage = null;
      });

      _scrollToBottom();

      // Send message via service
      await _messagingService.sendMessage(
        widget.conversation.id,
        content,
        replyToId: _replyToMessage?.id,
      );

      // Update message status
      setState(() {
        final index = _messages.indexWhere((m) => m.id == tempMessage.id);
        if (index != -1) {
          _messages[index] = tempMessage.copyWith(status: MessageStatus.sent);
        }
      });
    } catch (e) {
      _showError('Failed to send message: $e');
      // Remove failed message
      setState(() {
        _messages.removeWhere((m) => m.id == DateTime.now().millisecondsSinceEpoch.toString());
      });
    } finally {
      setState(() {
        _isSending = false;
      });
    }
  }

  Future<void> _sendFileMessage() async {
    try {
      final XFile? file = await _imagePicker.pickImage(source: ImageSource.gallery);
      if (file == null) return;

      setState(() {
        _isSending = true;
      });

      await _messagingService.sendFileMessage(
        widget.conversation.id,
        File(file.path),
      );
    } catch (e) {
      _showError('Failed to send file: $e');
    } finally {
      setState(() {
        _isSending = false;
      });
    }
  }

  void _onTyping() {
    if (!_isTyping) {
      _isTyping = true;
      _messagingService.sendTyping(widget.conversation.id);
      
      // Stop typing after 3 seconds of inactivity
      Future.delayed(const Duration(seconds: 3), () {
        if (_isTyping) {
          _isTyping = false;
          _messagingService.stopTyping(widget.conversation.id);
        }
      });
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _setReplyToMessage(EnhancedMessage message) {
    setState(() {
      _replyToMessage = message;
    });
  }

  Future<void> _joinVideoCall(String meetingUrl) async {
    try {
      final success = await _videoCallService.joinVideoCall(meetingUrl);
      if (!success) {
        _showError('Failed to join video call');
      }
    } catch (e) {
      _showError('Error joining video call: $e');
    }
  }

  void _cancelReply() {
    setState(() {
      _replyToMessage = null;
    });
  }

  void _scheduleMeeting() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MeetingSchedulingScreen(
          conversation: widget.conversation,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.conversation.getOtherUserName('current_user_id'),
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Text(
              widget.conversation.projectTitle,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.normal),
            ),
          ],
        ),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _scheduleMeeting,
            icon: const Icon(Icons.video_call),
            tooltip: 'Schedule Meeting',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'view_project':
                  // Navigate to project details
                  break;
                case 'view_profile':
                  // Navigate to user profile
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'view_project',
                child: Text('View Project'),
              ),
              const PopupMenuItem(
                value: 'view_profile',
                child: Text('View Profile'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Messages list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator(color: Colors.amber))
                : _messages.isEmpty
                    ? const Center(
                        child: Text(
                          'No messages yet.\nStart the conversation!',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    : ListView.builder(
                        controller: _scrollController,
                        reverse: true,
                        padding: const EdgeInsets.all(16),
                        itemCount: _messages.length + (_otherUserTyping != null ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == 0 && _otherUserTyping != null) {
                            return _buildTypingIndicator();
                          }
                          
                          final messageIndex = _otherUserTyping != null ? index - 1 : index;
                          final message = _messages[messageIndex];
                          return _buildMessageBubble(message);
                        },
                      ),
          ),
          
          // Reply preview
          if (_replyToMessage != null) _buildReplyPreview(),
          
          // Message input
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: Colors.grey[300],
            child: Text(
              _otherUserTyping!.substring(0, 1).toUpperCase(),
              style: const TextStyle(fontSize: 12),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(18),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '$_otherUserTyping is typing',
                  style: const TextStyle(
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[400]!),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(EnhancedMessage message) {
    final isMe = message.senderId == 'current_user_id';
    final isConsecutive = _messages.indexOf(message) < _messages.length - 1 &&
        _messages[_messages.indexOf(message) + 1].senderId == message.senderId;

    return Padding(
      padding: EdgeInsets.only(
        bottom: isConsecutive ? 2 : 8,
        left: isMe ? 50 : 0,
        right: isMe ? 0 : 50,
      ),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe && !isConsecutive)
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey[300],
              child: Text(
                message.senderName.substring(0, 1).toUpperCase(),
                style: const TextStyle(fontSize: 12),
              ),
            ),
          if (!isMe && isConsecutive) const SizedBox(width: 32),
          const SizedBox(width: 8),
          Flexible(
            child: GestureDetector(
              onLongPress: () => _showMessageOptions(message),
              child: Column(
                crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                children: [
                  // Reply preview
                  if (message.isReply && message.replyToMessage != null)
                    _buildReplyBubble(message.replyToMessage!, isMe),

                  // Main message bubble
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                    decoration: BoxDecoration(
                      color: isMe ? Colors.amber : Colors.grey[200],
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Message content
                        if (message.type == MessageType.text)
                          Text(
                            message.content,
                            style: TextStyle(
                              color: isMe ? Colors.white : Colors.black87,
                              fontSize: 16,
                            ),
                          ),

                        const SizedBox(height: 4),

                        // Message metadata
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              DateFormat('HH:mm').format(message.timestamp),
                              style: TextStyle(
                                fontSize: 12,
                                color: isMe ? Colors.white70 : Colors.grey[600],
                              ),
                            ),
                            if (message.isEdited) ...[
                              const SizedBox(width: 4),
                              Text(
                                '(edited)',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontStyle: FontStyle.italic,
                                  color: isMe ? Colors.white70 : Colors.grey[600],
                                ),
                              ),
                            ],
                            if (isMe) ...[
                              const SizedBox(width: 4),
                              Icon(
                                message.isRead ? Icons.done_all : Icons.done,
                                size: 16,
                                color: message.isRead ? Colors.blue : Colors.white70,
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReplyBubble(EnhancedMessage replyMessage, bool isMe) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: (isMe ? Colors.amber.withValues(alpha: 0.1) : Colors.grey[100]),
        borderRadius: BorderRadius.circular(8),
        border: Border(
          left: BorderSide(
            color: isMe ? Colors.amber : Colors.grey,
            width: 3,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            replyMessage.senderName,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: isMe ? Colors.amber[800] : Colors.grey[700],
            ),
          ),
          const SizedBox(height: 2),
          Text(
            replyMessage.content,
            style: TextStyle(
              fontSize: 12,
              color: isMe ? Colors.amber[700] : Colors.grey[600],
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildReplyPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      color: Colors.grey[100],
      child: Row(
        children: [
          Container(
            width: 3,
            height: 40,
            color: Colors.amber,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Replying to ${_replyToMessage!.senderName}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.amber,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _replyToMessage!.content,
                  style: const TextStyle(fontSize: 14),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _cancelReply,
            icon: const Icon(Icons.close, size: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: _sendFileMessage,
            icon: const Icon(Icons.attach_file, color: Colors.grey),
          ),
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type a message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 10,
                ),
              ),
              maxLines: null,
              textCapitalization: TextCapitalization.sentences,
              onChanged: (_) => _onTyping(),
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            decoration: const BoxDecoration(
              color: Colors.amber,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _isSending ? null : _sendMessage,
              icon: _isSending
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.send, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _showMessageOptions(EnhancedMessage message) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.reply),
              title: const Text('Reply'),
              onTap: () {
                Navigator.pop(context);
                _setReplyToMessage(message);
              },
            ),
            if (message.senderId == 'current_user_id') ...[
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('Edit'),
                onTap: () {
                  Navigator.pop(context);
                  // Implement edit functionality
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Delete', style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  // Implement delete functionality
                },
              ),
            ],
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Copy'),
              onTap: () {
                Navigator.pop(context);
                // Implement copy functionality
              },
            ),
          ],
        ),
      ),
    );
  }
}
