import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/job_listing.dart';
import '../config/app_config.dart';

class JobService {
  // Base URL for API endpoints
  final String baseUrl = AppConfig.apiBaseUrl + '/projects';
  
  // Headers for API authentication
  Future<Map<String, String>> get _headers async {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${await _getAuthToken()}',
    };
  }

  // Get auth token from secure storage
  Future<String> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token') ?? '';
  }

  // Get user ID from secure storage
  Future<String> _getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('user_id') ?? '';
  }

  // Create a new job listing
  Future<JobListing> createJobListing(JobListing jobListing) async {
    try {
      final headers = await _headers;
      final response = await http.post(
        Uri.parse(baseUrl),
        headers: headers,
        body: json.encode(jobListing.toJson()),
      );

      if (response.statusCode == 201) {
        return JobListing.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to create job listing: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error creating job listing: $e');
    }
  }

  // Get all job listings
  Future<List<JobListing>> getAllJobListings() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse(baseUrl),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => JobListing.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load job listings: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading job listings: $e');
    }
  }

  // Get job listings for a specific employer
  Future<List<JobListing>> getEmployerJobListings() async {
    try {
      final headers = await _headers;
      final userId = await _getUserId();
      final response = await http.get(
        Uri.parse('$baseUrl/employer/$userId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => JobListing.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load employer job listings: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading employer job listings: $e');
    }
  }

  // Get job listings that match a job seeker's profile
  Future<List<JobListing>> getMatchedJobListings() async {
    try {
      final headers = await _headers;
      final userId = await _getUserId();
      final response = await http.get(
        Uri.parse('$baseUrl/matches/$userId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => JobListing.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load matched job listings: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading matched job listings: $e');
    }
  }

  // Get a specific job listing by ID
  Future<JobListing> getJobListingById(String id) async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$baseUrl/$id'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return JobListing.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to load job listing: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading job listing: $e');
    }
  }

  // Update a job listing
  Future<JobListing> updateJobListing(String id, JobListing jobListing) async {
    try {
      final headers = await _headers;
      final response = await http.put(
        Uri.parse('$baseUrl/$id'),
        headers: headers,
        body: json.encode(jobListing.toJson()),
      );

      if (response.statusCode == 200) {
        return JobListing.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to update job listing: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating job listing: $e');
    }
  }

  // Delete a job listing
  Future<bool> deleteJobListing(String id) async {
    try {
      final headers = await _headers;
      final response = await http.delete(
        Uri.parse('$baseUrl/$id'),
        headers: headers,
      );

      return response.statusCode == 204;
    } catch (e) {
      throw Exception('Error deleting job listing: $e');
    }
  }

  // Apply to a job listing
  Future<bool> applyToJobListing(String jobId) async {
    try {
      final headers = await _headers;
      final userId = await _getUserId();
      final response = await http.post(
        Uri.parse('$baseUrl/$jobId/apply'),
        headers: headers,
        body: json.encode({'userId': userId}),
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error applying to job listing: $e');
    }
  }

  // Accept a job listing (for job seekers)
  Future<bool> acceptJobListing(String jobId) async {
    try {
      final headers = await _headers;
      final userId = await _getUserId();
      final response = await http.post(
        Uri.parse('$baseUrl/$jobId/accept'),
        headers: headers,
        body: json.encode({'userId': userId}),
      );

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error accepting job listing: $e');
    }
  }

  // Get mock job listings for testing
  Future<List<JobListing>> getMockJobListings() async {
    // Return mock data for testing
    return [
      JobListing(
        id: '507f1f77bcf86cd799439031',
        employer: 'Tech Solutions Inc.',
        title: 'Mobile App Development',
        description: 'Developing a new mobile app for our client.',
        skills: ['Flutter', 'Dart', 'Firebase'],
        location: 'New York, NY',
        workType: 'full-time',
        budget: Budget(min: 5000, max: 8000),
        duration: '3 months',
        deadline: DateTime.now().add(const Duration(days: 90)),
        experienceLevel: 'intermediate',
        projectType: 'fixed-price',
        status: 'open',
        applications: [],
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
      JobListing(
        id: '507f1f77bcf86cd799439032',
        employer: 'Marketing Experts',
        title: 'Digital Marketing Campaign',
        description: 'Planning and executing a digital marketing campaign for a major client.',
        skills: ['Digital Marketing', 'SEO', 'Social Media'],
        location: 'Chicago, IL',
        workType: 'contract',
        budget: Budget(min: 3000, max: 5000),
        duration: '2 months',
        deadline: DateTime.now().add(const Duration(days: 60)),
        experienceLevel: 'expert',
        projectType: 'fixed-price',
        status: 'open',
        applications: [],
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
      JobListing(
        id: '507f1f77bcf86cd799439033',
        employer: 'Design Studio',
        title: 'Website Redesign',
        description: 'Redesigning the company website with modern UI/UX principles.',
        skills: ['UI/UX Design', 'Figma', 'Adobe XD'],
        location: 'San Francisco, CA',
        workType: 'part-time',
        budget: Budget(min: 2000, max: 4000),
        duration: '1 month',
        deadline: DateTime.now().add(const Duration(days: 30)),
        experienceLevel: 'entry',
        projectType: 'fixed-price',
        status: 'open',
        applications: [],
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];
  }
}


